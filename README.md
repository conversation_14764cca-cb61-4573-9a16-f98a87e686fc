# MCG_Meta: MCG三阶段智能解析项目

## 项目概述

MCG_Meta是一个基于深度学习的心磁信号（MCG）智能解析项目，采用"**基础构建 → 精准应用 → 科学发现**"的三阶段战略架构。

### 三阶段战略

- **阶段一 (Foundation)**: 通用信号表征学习 - 使用MAE自监督预训练构建基础编码器
- **阶段二 (Expertise)**: 精准应用 - 构建"临床问题-人群"专家模型矩阵  
- **阶段三 (Discovery)**: 科学发现 - 跨人群模型归因与特征洞察

### 当前状态

🔵 **第一阶段开发中** - 基础编码器MAE预训练

## 项目架构

```
MCG_Meta/
├── configs/          # 配置文件
├── src/             # 源代码
│   ├── data/        # 数据处理
│   ├── models/      # 模型架构
│   ├── training/    # 训练模块
│   ├── evaluation/  # 评估模块
│   └── utils/       # 工具模块
├── experiments/     # 实验记录
├── tests/          # 单元测试
└── docs/           # 文档
```

## 核心特性

### 🎯 第一阶段特性

- **混合时空MAE架构**: 空间CNN + 时间Transformer + Cross-attention融合
- **多策略数据处理**: 可配置归一化、padding和噪声策略
- **混合掩码策略**: 时空块掩码 + 随机点掩码
- **完整可视化系统**: 基于MCGVisualizer的信号分析工具
- **三阶段兼容设计**: 标准接口预留后续扩展

### 📊 数据处理能力

- 支持36通道MCG信号处理
- 多种归一化策略: sample_level, global, channel_wise, robust
- 灵活的时序长度统一: reflect padding, 目标长度1500
- 渐进式噪声配置: 从高斯噪声开始，可扩展至生理噪声

### 🔧 技术栈

- **深度学习**: PyTorch 2.0+, 混合精度训练
- **数据处理**: NumPy, Pandas, SciPy
- **可视化**: Matplotlib, OpenCV
- **配置管理**: YAML配置 + 层次化管理
- **实验追踪**: TensorBoard, WandB支持

## 快速开始

### 环境配置

```bash
# 克隆项目
git clone <repository_url>
cd MCG_Meta

# 安装依赖
pip install -r requirements.txt

# 配置数据路径
# 编辑 configs/preprocessing.yaml 中的 txt_data_dir
```

### 运行第一阶段训练

```bash
# 数据预处理和质量检查
python -m src.data.preprocessing --config configs/preprocessing.yaml

# 启动MAE预训练
python -m src.training.mae_trainer --config configs/stage1_mae.yaml

# 可视化结果
python -m src.evaluation.visualizer --checkpoint checkpoints/best_model.pth
```

## 配置说明

### 数据预处理配置 (`configs/preprocessing.yaml`)

- **归一化策略**: 支持样本级、全局、通道级和鲁棒归一化
- **时序处理**: 反射填充统一至1500时间点
- **质量控制**: 异常值检测、NaN值处理
- **噪声配置**: 渐进式噪声，可扩展配置

### MAE训练配置 (`configs/stage1_mae.yaml`)

- **模型架构**: 混合时空编码器 + 标准MAE解码器
- **掩码策略**: 时间块、空间块、随机点的混合策略
- **训练设置**: AdamW优化器 + Cosine学习率调度
- **评估体系**: 重建指标 + 表征质量指标

## 开发进展

### ✅ 已完成
- [x] 项目架构设计
- [x] 配置系统建立
- [x] 基础模块结构

### 🔄 进行中
- [ ] 数据预处理系统实现
- [ ] MAE模型架构开发
- [ ] 训练和评估系统

### 📋 计划中
- [ ] 完整实验验证
- [ ] 性能优化和测试
- [ ] 文档完善

## 贡献指南

1. 遵循代码规范 (Black + isort + flake8)
2. 编写单元测试
3. 更新相关文档
4. 提交前运行完整测试

## 许可证

本项目用于学术研究目的。

---

*MCG_Meta项目 - 让AI模型从诊断工具升级为科研发现引擎*