#!/usr/bin/env python3
"""
MAE Phase 1 轻量级烟雾测试
========================

验证MAE模型的核心功能：
1. 模型创建和前向传播
2. 掩码生成和设备对齐
3. 重建损失计算
4. 基本的数值稳定性

使用最小配置避免内存问题
"""

import torch
import numpy as np
import sys
from pathlib import Path

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_mae_forward():
    """测试MAE前向传播"""
    print("=== 测试MAE前向传播 ===")
    
    try:
        from models.mae.mae_model import create_mae_model
        
        # 最小配置 - 确保维度匹配
        config = {
            'encoder': {
                'hidden_dim': 64,
                'spatial_encoder': {
                    'type': 'mcg_spatial',
                    'conv_channels': [8, 16],
                    'dropout': 0.0,
                    'pooling': 'adaptive',
                    'use_attention': False
                },
                'temporal_encoder': {
                    'type': 'mcg_temporal',
                    'd_model': 64,
                    'nhead': 2,
                    'num_layers': 1,
                    'dropout': 0.0,
                    'global_pooling': 'attention'
                },
                'fusion_module': {
                    'fusion_type': 'cross_attention',
                    'spatial_dim': 256,  # 空间编码器输出维度
                    'temporal_dim': 64,  # 时间编码器输出维度
                    'hidden_dim': 64,
                    'dropout': 0.0
                }
            },
            'decoder': {
                'encoder_dim': 64,  # 匹配encoder hidden_dim
                'decoder_dim': 32,
                'num_heads': 2,
                'num_layers': 1,
                'output_channels': 36,
                'output_seq_len': 100  # 缩短序列长度
            },
            'masking': {
                'type': 'temporal_block', 
                'mask_ratio': 0.2, 
                'block_size_range': [5, 15]
            },
            'loss_type': 'mse',
            'normalize_target': True,
            'mask_loss_only': True
        }
        
        model = create_mae_model(config)
        model.eval()
        
        # 小批次测试
        batch_size, seq_len, channels = 2, 100, 36
        x = torch.randn(batch_size, seq_len, channels)
        
        print(f"输入形状: {x.shape}")
        print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
        
        with torch.no_grad():
            result = model(x)
        
        print(f"✅ 前向传播成功")
        print(f"  - 重建形状: {result['reconstructed'].shape}")
        print(f"  - 掩码形状: {result['mask'].shape}")
        print(f"  - 损失值: {result['total_loss'].item():.6f}")
        print(f"  - 掩码比例: {(1 - result['mask'].mean()).item():.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mask_device_alignment():
    """测试掩码设备对齐"""
    print("\n=== 测试掩码设备对齐 ===")
    
    try:
        from models.mae.masking_strategy import create_masking_strategy
        
        config = {
            'type': 'temporal_block',
            'mask_ratio': 0.3,
            'block_size_range': [5, 10]
        }
        
        strategy = create_masking_strategy(config)
        
        # CPU测试
        batch_size, seq_len, channels = 2, 50, 36
        mask_cpu = strategy.generate_mask(batch_size, seq_len, channels)
        
        print(f"CPU掩码形状: {mask_cpu.shape}, 设备: {mask_cpu.device}")
        print(f"掩码比例: {(1 - mask_cpu.mean()).item():.2%}")
        
        # GPU测试（如果可用）
        if torch.cuda.is_available():
            x_gpu = torch.randn(batch_size, seq_len, channels).cuda()
            mask_aligned = mask_cpu.to(x_gpu.device).type_as(x_gpu)
            
            print(f"GPU对齐后: {mask_aligned.device}, dtype: {mask_aligned.dtype}")
            print(f"✅ 设备对齐测试通过")
        else:
            print("⚠️  GPU不可用，跳过GPU测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 掩码设备对齐失败: {e}")
        return False

def test_validation_dataloader_fallback():
    """测试验证数据加载器回退机制"""
    print("\n=== 测试验证数据回退 ===")
    
    try:
        # 模拟训练器配置
        config = {
            'validation': {'enabled': True},
            'data_split': {'train_ratio': 0.8, 'val_ratio': 0.2, 'random_seed': 42},
            'dataloader': {'batch_size': 2, 'num_workers': 0}
        }
        
        # 检查是否有验证逻辑
        val_enabled = config.get('validation', {}).get('enabled', False)
        
        if val_enabled:
            print("✅ 验证配置已启用")
            print(f"  - 训练比例: {config['data_split']['train_ratio']}")
            print(f"  - 验证比例: {config['data_split']['val_ratio']}")
        else:
            print("⚠️  验证未启用，将跳过验证步骤")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证配置测试失败: {e}")
        return False

def test_config_consistency():
    """测试配置一致性"""
    print("\n=== 测试配置一致性 ===")
    
    try:
        # 检查stage1配置文件
        config_path = Path("configs/stage1_mae.yaml")
        
        if config_path.exists():
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                stage1_config = yaml.safe_load(f)
            
            # 检查关键配置项
            model_config = stage1_config.get('model', {})
            mae_config = model_config.get('mae_config', {})
            masking_config = mae_config.get('masking', {})
            
            print("✅ Stage1配置文件存在")
            print(f"  - 掩码策略: {masking_config.get('strategies', [])}")
            print(f"  - 调度启用: {masking_config.get('scheduling', {}).get('enabled', False)}")
            
            # 检查策略类型一致性
            strategies = masking_config.get('strategies', [])
            supported_types = ['temporal_block', 'spatial_block', 'random_point']
            
            for strategy in strategies:
                strategy_type = strategy.get('type', '')
                if strategy_type in supported_types:
                    print(f"  ✅ 支持的策略: {strategy_type}")
                else:
                    print(f"  ⚠️  未知策略: {strategy_type}")
        else:
            print("⚠️  Stage1配置文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置一致性检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("MCG Phase 1 (MAE) 烟雾测试")
    print("=" * 50)
    
    tests = [
        test_mae_forward,
        test_mask_device_alignment, 
        test_validation_dataloader_fallback,
        test_config_consistency
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结:")
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！Phase 1 基础功能正常")
    elif passed >= total * 0.75:
        print("⚠️  大部分测试通过，存在少量问题需要修复")
    else:
        print("❌ 多个测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
