# MCG_Meta 项目依赖
# 支持MCG信号处理、深度学习和可视化

# 深度学习框架
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# 数据处理
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# 可视化
matplotlib>=3.6.0
seaborn>=0.12.0
opencv-python>=4.7.0

# 机器学习工具
scikit-learn>=1.3.0

# 配置和日志
pyyaml>=6.0
tensorboard>=2.12.0
wandb>=0.15.0  # 可选

# 数据增强和信号处理
librosa>=0.10.0  # 音频信号处理工具，可用于时序数据
wfdb>=4.1.0      # 生物医学信号处理

# 实用工具
tqdm>=4.65.0
rich>=13.3.0     # 美化终端输出
typer>=0.9.0     # CLI工具 
click>=8.1.0

# 开发和测试
pytest>=7.3.0
pytest-cov>=4.1.0
black>=23.0.0    # 代码格式化
isort>=5.12.0    # 导入排序
flake8>=6.0.0    # 代码检查

# 类型检查
mypy>=1.3.0

# 文档生成
sphinx>=6.2.0
sphinx-rtd-theme>=1.2.0

# Jupyter支持 (用于数据探索)
jupyter>=1.0.0
ipykernel>=6.23.0
ipywidgets>=8.0.0