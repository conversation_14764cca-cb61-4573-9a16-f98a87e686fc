

class MCGVisualizer:
    """
    一个用于可视化36通道MCG数据的多功能绘图类。

    V3.0 更新:
    - 修复了插值磁图对比时 colorbar 的重叠问题。
    - 增加了所有通道与参考通道的平均相似度计算和显示。
    - 新增“堆叠对比”功能，可在第三列显示两组数据的差异图。
    - 新增“显示参考通道”功能，可在时间波组图下方绘制参考通道波形。
    - 恢复了空间波组图的网格和外围刻度，并移除了 plt.style 的使用。


    # 绘图示例
    # 绘图
    # print("--- 案例一: 分析单组数据 (data1) ---")
    # vis_single = MEGVisualizer(origin_data_select_mean_signal_channels, data1_name="原始信号")
    # vis_single.plot_spatiotemporal_grid()
    # vis_single.plot_temporal_overlay()
    # vis_single.plot_interpolated_map()
    #
    # # === 案例二: 两组数据对比 ===
    # print("\n--- 案例二: 对比两组数据 (data1 vs data2) ---")
    # vis_compare = MEGVisualizer(origin_data_select_mean_signal_channels, origin_data_select_mean_signal_channels, data1_name="原始信号", data2_name="高噪信号")
    # vis_compare.plot_spatiotemporal_grid(suptitle="时空波组图对比")
    # vis_compare.plot_temporal_overlay(suptitle="时间波组图对比")
    # vis_compare.plot_interpolated_map(suptitle="插值磁图对比")  # 自动寻找两组数据中峰值最大的时间点

    # # === 案例三: 与标准参考数据进行对比 ===
    # print("\n--- 案例三: 分析单组数据并与参考通道对比 ---")
    # vis_ref = MEGVisualizer(origin_data_select_mean_signal_channels, reference_channel=origin_data_select_mean_signal[:,-1], data1_name="原始信号")
    # # 在6x6网格图上，每个子图会显示与参考信号的相关系数(R)
    # vis_ref.plot_spatiotemporal_grid(suptitle="时空波组图 (与参考通道对比)")

    # # 也可以查看计算出的指标
    # print("与参考通道的相似度指标:")
    # print(vis_ref.similarity_results)
    #
    # # === 案例四: 两组数据都与参考数据对比 ===
    # print("\n--- 案例四: 对比两组数据，并同时显示它们与参考通道的相似度 ---")
    # vis_compare_ref = MEGVisualizer(origin_data_select_mean_signal_channels, origin_data_select_mean_signal_channels, reference_channel=origin_data_select_mean_signal[:,-1],
    #                                 data1_name="原始信号", data2_name="高噪信号")
    # vis_compare_ref.plot_spatiotemporal_grid(suptitle="时空波组图对比 (与参考通道)")
    """

    def __init__(self, data1, data2=None, reference_channel=None,
                 data1_name='Data 1', data2_name='Data 2',
                 show_stacked_diff=True, show_reference_channel=True):
        """
        初始化Visualizer。

        参数:
        - data1, data2, reference_channel, data1_name, data2_name: (同前)
        - show_stacked_diff (bool): 是否在对比图中显示第三列的差异图 (data1 - data2)。
        - show_reference_channel (bool): 是否在时间波组图下方显示参考通道。
        """
        # --- 数据校验与存储 ---
        if data1.shape[1] != 36:
            raise ValueError("输入数据 data1 的第二维必须是36 (通道数)")
        self.data1 = data1
        self.data1_6x6 = self.data1.reshape(self.data1.shape[0], 6, 6)
        self.data1_name = data1_name

        self.data2 = None
        self.diff_data = None
        if data2 is not None:
            if data1.shape != data2.shape:
                raise ValueError("对比数据 data1 和 data2 必须有相同的形状")
            self.data2 = data2
            self.data2_6x6 = self.data2.reshape(self.data2.shape[0], 6, 6)
            self.diff_data = self.data1 - self.data2
        self.data2_name = data2_name

        self.show_stacked_diff = show_stacked_diff and (self.data2 is not None)
        self.show_reference_channel = show_reference_channel and (reference_channel is not None)

        # --- 计算统一的Y轴范围 ---
        all_data_list = [self.data1]
        if self.data2 is not None: all_data_list.append(self.data2)
        if self.diff_data is not None and self.show_stacked_diff: all_data_list.append(self.diff_data)

        all_data = np.concatenate(all_data_list, axis=0)
        data_min = np.min(all_data)
        data_max = np.max(all_data)
        padding = (data_max - data_min) * 0.1
        self.ylim = (data_min - padding, data_max + padding)

        # --- 与参考通道计算相似度 ---
        self.reference_channel = reference_channel
        self.similarity_results = {}
        if self.reference_channel is not None:
            self._calculate_similarity_metrics()

    def _calculate_similarity_metrics(self):
        """[ENHANCE] 计算每个通道与参考通道的相似度，并计算平均值。"""
        if self.reference_channel is None: return

        def compute_metrics(data, data_name):
            metrics = {}
            correlations = []
            for i in range(36):
                min_len = min(len(data[:, i]), len(self.reference_channel))
                channel_data = data[:min_len, i]
                ref_data = self.reference_channel[:min_len]
                corr, _ = pearsonr(channel_data, ref_data)
                mse = np.mean((channel_data - ref_data) ** 2)
                metrics[i] = {'correlation': corr, 'mse': mse}
                correlations.append(corr)

            # 计算平均相似度 (使用绝对值的平均值)
            self.similarity_results[data_name] = {
                'channels': metrics,
                'mean_correlation': np.mean(np.abs(correlations))
            }

        compute_metrics(self.data1, self.data1_name)
        if self.data2 is not None:
            compute_metrics(self.data2, self.data2_name)

    def _plot_single_grid(self, fig, axes, data, title, data_name, show_similarity=True, data2=None, data2_name=None):
        """辅助函数：在给定的 axes 网格上绘制数据

        参数:
        - data2: 第二组数据（用于堆叠显示）
        - data2_name: 第二组数据名称
        """
        sim_data = self.similarity_results.get(data_name, {})
        sim_data2 = self.similarity_results.get(data2_name, {}) if data2_name else {}
        primary_color = '#00529B' if data_name != 'Difference' else '#D81B60'
        secondary_color = '#D81B60' if data2 is not None else None

        for i, ax in enumerate(axes.flatten()):
            # 绘制第一组数据
            ax.plot(data[:, i], color=primary_color, linewidth=1.2, label=data_name if data2 is not None else None)

            # 如果有第二组数据，堆叠显示
            if data2 is not None:
                ax.plot(data2[:, i], color=secondary_color, linewidth=1.2, alpha=0.8, label=data2_name)
                if i == 0:  # 只在第一个子图添加图例
                    ax.legend(loc='upper right', fontsize=6)

            ax.grid(True, linestyle='--', alpha=0.5)

            # 在左上角标注通道号
            ax.text(0.05, 0.95, f'Ch{i + 1}', transform=ax.transAxes, fontsize=8,
                    verticalalignment='top', bbox=dict(boxstyle='round,pad=0.2', fc='white', alpha=0.7, ec='none'))

            # 如果有相似度结果，则在右上角标注
            if show_similarity and sim_data and 'channels' in sim_data:
                corr = sim_data['channels'][i]['correlation']
                corr_text = f'R1={corr:.2f}'

                # 如果有第二组数据的相似度，也显示
                if sim_data2 and 'channels' in sim_data2:
                    corr2 = sim_data2['channels'][i]['correlation']
                    corr_text += f'\nR2={corr2:.2f}'

                ax.text(0.95, 0.95, corr_text, transform=ax.transAxes, fontsize=7,
                        horizontalalignment='right', verticalalignment='top',
                        bbox=dict(boxstyle='round,pad=0.2', fc='lightgoldenrodyellow', alpha=0.8, ec='none'))

        # 添加大标题
        title_text = title
        if show_similarity and sim_data and 'mean_correlation' in sim_data:
            mean_corr = sim_data['mean_correlation']
            title_text += f'\n(Avg. Correlation: {mean_corr:.3f})'

            # 如果有第二组数据的平均相似度，也显示
            if sim_data2 and 'mean_correlation' in sim_data2:
                mean_corr2 = sim_data2['mean_correlation']
                title_text += f' | {data2_name}: {mean_corr2:.3f}'

        axes[0, 0].set_title(title_text, fontsize=16, loc='left', x=-0.1, y=1.25)

    def plot_spatiotemporal_grid(self, suptitle="时空波组图 (Spatiotemporal Grid Plot)", stack=False, savefile=None):
        """绘制6x6的时空波组图，支持对比和堆叠差异显示。

        参数:
        - suptitle: 图表标题
        - stack: 是否堆叠显示两组数据（在同一子图中显示）
        - savefile: 保存文件路径，如果提供则保存图片而不显示
        """
        # 根据stack参数决定布局
        if stack and self.data2 is not None:
            # 堆叠模式：只需要一列，两组数据在同一子图中显示
            num_rows, num_cols = 6, 6 
            fig_width = 20 
            fig_height = 15 # 也可以适当增加高度，让子图不那么扁
            fig, all_axes = plt.subplots(num_rows, num_cols, figsize=(fig_width, fig_height), sharex=True, sharey=True)
            fig.suptitle(suptitle, fontsize=22, y=0.98)

            # 调整Y轴范围
            plt.ylim(self.ylim)

            # 堆叠显示两组数据
            self._plot_single_grid(fig, all_axes, self.data1, f"{self.data1_name} vs {self.data2_name}",
                                 self.data1_name, show_similarity=True,
                                 data2=self.data2, data2_name=self.data2_name)
        else:
            # 原有的并排显示模式
            num_cols = 1
            if self.data2 is not None:
                num_cols = 3 if self.show_stacked_diff else 2

            fig_width = 10 * num_cols
            fig, all_axes = plt.subplots(6, 6 * num_cols, figsize=(fig_width, 10), sharex=True, sharey=True)
            fig.suptitle(suptitle, fontsize=22, y=0.98)

            # 调整Y轴范围
            plt.ylim(self.ylim)

            # 绘制第一组数据
            axes1 = all_axes[:, :6] if num_cols > 1 else all_axes
            self._plot_single_grid(fig, axes1, self.data1, self.data1_name, self.data1_name)

            if self.data2 is not None:
                # 绘制第二组数据
                axes2 = all_axes[:, 6:12]
                self._plot_single_grid(fig, axes2, self.data2, self.data2_name, self.data2_name)

                if self.show_stacked_diff:
                    # 绘制差异图
                    axes3 = all_axes[:, 12:18]
                    self._plot_single_grid(fig, axes3, self.diff_data, 'Difference', 'Difference', show_similarity=False)

        plt.tight_layout(rect=[0, 0, 1, 0.93])

        # 根据savefile参数决定是保存还是显示
        if savefile:
            plt.savefig(savefile, dpi=300, bbox_inches='tight')
            plt.close()  # 关闭图形以释放内存
        else:
            plt.show()

    def _plot_single_overlay(self, ax, data, title, data_name):
        """辅助函数：在指定的Axes上绘制一个叠加图。"""
        colors = plt.cm.viridis(np.linspace(0, 1, 36))
        for i in range(36):
            ax.plot(data[:, i], color=colors[i], alpha=0.7, label=f'Ch {i + 1}')
        ax.grid(True, linestyle='--', alpha=0.6)

        title_text = title
        sim_data = self.similarity_results.get(data_name)
        if sim_data and 'mean_correlation' in sim_data:
            mean_corr = sim_data['mean_correlation']
            title_text += f'\n(Avg. Correlation: {mean_corr:.3f})'
        ax.set_title(title_text, fontsize=16)

        ax.set_xlabel("Time Points", fontsize=12)
        ax.set_ylabel("Amplitude", fontsize=12)
        return ax

    def plot_temporal_overlay(self, suptitle="时间波组图 (Temporal Overlay Plot)", savefile=None):
        """绘制叠加图，支持对比、堆叠差异和显示参考通道。

        参数:
        - suptitle: 图表标题
        - savefile: 保存文件路径，如果提供则保存图片而不显示
        """
        num_cols = 1
        if self.data2 is not None:
            num_cols = 3 if self.show_stacked_diff else 2

        num_rows = 2 if self.show_reference_channel else 1
        height_ratios = [4, 1] if self.show_reference_channel else [1]

        fig = plt.figure(figsize=(8 * num_cols, 6 * num_rows))
        gs = gridspec.GridSpec(num_rows, num_cols, figure=fig, height_ratios=height_ratios, hspace=0.4)
        fig.suptitle(suptitle, fontsize=20)

        # --- 绘制主图 ---
        ax1 = self._plot_single_overlay(fig.add_subplot(gs[0, 0]), self.data1, self.data1_name, self.data1_name)
        ax1.set_ylim(self.ylim)

        if self.data2 is not None:
            ax2 = self._plot_single_overlay(fig.add_subplot(gs[0, 1], sharey=ax1), self.data2, self.data2_name,
                                            self.data2_name)
            plt.setp(ax2.get_yticklabels(), visible=False)
            ax2.set_ylabel("")

            if self.show_stacked_diff:
                ax3 = self._plot_single_overlay(fig.add_subplot(gs[0, 2], sharey=ax1), self.diff_data, 'Difference',
                                                'Difference')
                plt.setp(ax3.get_yticklabels(), visible=False)
                ax3.set_ylabel("")

        # --- 绘制参考通道 ---
        if self.show_reference_channel:
            ref_ax = fig.add_subplot(gs[1, :])  # 跨越所有列
            ref_ax.plot(self.reference_channel, color='black', linewidth=1.5)
            ref_ax.set_title("Reference Channel", fontsize=14)
            ref_ax.set_xlabel("Time Points", fontsize=12)
            ref_ax.set_ylabel("Amplitude", fontsize=12)
            ref_ax.grid(True, linestyle='--', alpha=0.6)
            ref_ax.autoscale(enable=True, axis='x', tight=True)

        plt.tight_layout(rect=[0, 0, 1, 0.95])

        # 根据savefile参数决定是保存还是显示
        if savefile:
            plt.savefig(savefile, dpi=300, bbox_inches='tight')
            plt.close()  # 关闭图形以释放内存
        else:
            plt.show()

    def _plot_single_map(self, ax, data_6x6, time_point, title):
        """辅助函数：绘制单个插值磁图。"""
        num_samples = data_6x6.shape[0]
        if time_point is None:
            time_point = np.unravel_index(np.argmax(np.abs(data_6x6)), data_6x6.shape)[0]
        time_point = max(0, min(time_point, num_samples - 1))

        data_2d = data_6x6[time_point, :, :]
        data_interpolated = cv2.resize(data_2d, (200, 200), interpolation=cv2.INTER_CUBIC)
        vmax = np.max(np.abs(data_interpolated))

        im = ax.imshow(data_interpolated, cmap='coolwarm', vmin=-vmax, vmax=vmax, origin='lower')
        ax.set_title(f"{title}\nTime Point: {time_point}", fontsize=14)
        ax.set_xticks([])
        ax.set_yticks([])
        return im, vmax

    def plot_interpolated_map(self, time_point=None, suptitle="插值磁图 (Interpolated Map)", savefile=None):
        """[FIX] 绘制插值磁图，修复了colorbar重叠问题。

        参数:
        - time_point: 时间点
        - suptitle: 图表标题
        - savefile: 保存文件路径，如果提供则保存图片而不显示
        """
        if self.data2 is None:
            fig, ax = plt.subplots(figsize=(7, 6))
            im, _ = self._plot_single_map(ax, self.data1_6x6, time_point, self.data1_name)
            fig.colorbar(im, ax=ax, label='Amplitude', fraction=0.046, pad=0.04)
        else:
            fig, axes = plt.subplots(1, 2, figsize=(12, 6))
            im1, vmax1 = self._plot_single_map(axes[0], self.data1_6x6, time_point, self.data1_name)
            im2, vmax2 = self._plot_single_map(axes[1], self.data2_6x6, time_point, self.data2_name)

            vmax_global = max(vmax1, vmax2) if vmax1 > 0 and vmax2 > 0 else max(abs(vmax1), abs(vmax2))
            im1.set_clim(-vmax_global, vmax_global)
            im2.set_clim(-vmax_global, vmax_global)

            # [FIX] 调整colorbar的位置，避免重叠
            fig.subplots_adjust(right=0.85)
            cbar_ax = fig.add_axes([0.88, 0.15, 0.04, 0.7])  # [left, bottom, width, height]
            fig.colorbar(im2, cax=cbar_ax, label='Amplitude')

        fig.suptitle(suptitle, fontsize=20, y=0.98)

        # 根据savefile参数决定是保存还是显示
        if savefile:
            plt.savefig(savefile, dpi=300, bbox_inches='tight')
            plt.close()  # 关闭图形以释放内存
        else:
            plt.show()

