# MCG三阶段战略项目技术传承文档

> **文档目的**：为MCG三阶段战略项目（Foundation → Expertise → Discovery）提供技术传承，重点传递数据理解、工具复用和经验总结，不约束新项目的架构设计自由度。
> 重要 ： 本文档属于常规的类第二阶段(没有预训练模型基座，但为了满足特定场景使用特定数据集进行了从零开始的代理标签(造影)加多任务/后期融合的训练)的训练实践项目经验总结，仅供参考

## 📊 1. MCG数据源深度解析

### 1.1 原始数据格式详解

**MCG信号数据** (`.txt文件`)
```
格式：n_timestamp × 37 矩阵（制表符分隔）
- 第1列：时间索引（需忽略）
- 第2-37列：36个空间通道的信号值
- n_timestamp：时间序列长度（样本间不一致，范围500-2000）
```

**关键发现**：
- 36个通道具有**空间几何关系**，可重排为6×6网格
- 时间序列长度的不一致性需要统一处理（padding/truncation）
- 信号包含噪声，需要适当的预处理和可能的去噪

**数据读取示例**：
```python
import pandas as pd
import numpy as np

def load_mcg_signal(txt_path):
    """读取MCG信号数据"""
    data = pd.read_csv(txt_path, sep='\t', header=None)
    # 忽略第一列索引，取36个通道
    signal_data = data.iloc[:, 1:37].values  # shape: [n_timestamp, 36]
    return signal_data.astype(np.float32)
```

### 1.2 标签体系详解

**Excel标签文件结构**：
- **Sheet '训练集'**: 训练样本ID + 部分标签
- **Sheet '内测集'**: 测试样本ID + 部分标签  
- **Sheet '训测集三支信息'**: 血管狭窄详细信息

**核心标签**：
1. **血管狭窄程度** (`LM`, `LAD`, `LCX`, `RCA`)：原始值0-100
2. **综合诊断标签** ('造影结论')：0/1二分类
3. **12项临床特征**：包含数值型和文本型，存在缺失值

**标签读取示例**：
```python
import pandas as pd

def load_labels(xlsx_path):
    """读取完整标签信息"""
    # 读取三个关键sheet
    train_sheet = pd.read_excel(xlsx_path, sheet_name='训练集')
    test_sheet = pd.read_excel(xlsx_path, sheet_name='内测集')  
    vessel_sheet = pd.read_excel(xlsx_path, sheet_name='训测集三支信息')
    
    # 通过'心磁号'合并数据
    combined_data = {}
    
    # 处理血管狭窄信息
    for _, row in vessel_sheet.iterrows():
        mcg_id = str(row['心磁号'])
        combined_data[mcg_id] = {
            'LM': row.get('LM', 0),
            'LAD': row.get('LAD', 0), 
            'LCX': row.get('LCX', 0),
            'RCA': row.get('RCA', 0)
        }
    
    return combined_data
```

### 1.3 数据规模与分布

- **总样本数**：约3000个MCG记录
- **训练/测试划分**：通过Excel sheet预定义
- **时序长度分布**：主要集中在500-1500，最大约2000
- **标签分布**：存在一定程度的类别不平衡

## 🛠️ 2. 验证过的数据处理策略

### 2.1 时序数据标准化 ⭐️ 

**核心决策：样本级归一化 vs 跨样本归一化**

```python
# 样本级归一化（推荐）- 适合个体差异大的生理信号
def normalize_per_sample(signal, method='standard'):
    """
    Args:
        signal: [n_timestamp, 36] 
        method: 'standard' | 'minmax' | 'maxabs'
    """
    if method == 'standard':
        return (signal - signal.mean()) / (signal.std() + 1e-8)
    elif method == 'minmax': 
        return (signal - signal.min()) / (signal.max() - signal.min() + 1e-8)
    elif method == 'maxabs':
        return signal / (np.abs(signal).max() + 1e-8)
```

**经验总结**：
- ✅ 样本级归一化保留个体特征，泛化性更好
- ❌ 跨样本归一化会被异常值影响，且丢失个体信息

### 2.2 时序长度统一策略

**方案对比**：
```python
def unify_sequence_length(signal, target_length=1500, method='pad'):
    """
    method: 'pad' | 'truncate' | 'resample'
    """
    current_length = signal.shape[0]
    
    if method == 'pad':
        if current_length < target_length:
            # 使用反射填充保持信号连续性
            pad_length = target_length - current_length
            signal = np.pad(signal, ((0, pad_length), (0, 0)), mode='reflect')
        else:
            signal = signal[:target_length]
    
    return signal
```

**建议**：target_length=1500 能覆盖大部分样本，避免过多padding

### 2.3 数据增强策略（在线增强）

```python
def online_augmentation(signal, config):
    """在训练过程中动态应用数据增强"""
    augmented = signal.copy()
    
    # 1. 高斯噪声
    if np.random.random() < config['noise_prob']:
        noise = np.random.normal(0, config['noise_std'], augmented.shape)
        augmented += noise
    
    # 2. 同步正弦噪声（跨所有通道）
    if np.random.random() < config['sine_prob']:
        freq = np.random.uniform(20, 50)  # 20-50Hz
        t = np.linspace(0, 1, augmented.shape[0])
        sine_noise = config['sine_amplitude'] * np.sin(2 * np.pi * freq * t)
        augmented += sine_noise[:, np.newaxis]
    
    # 3. 通道级随机缩放
    if np.random.random() < config['scale_prob']:
        scale_factors = np.random.uniform(
            config['scale_range'][0], config['scale_range'][1], 
            (1, augmented.shape[1])
        )
        augmented *= scale_factors
    
    return augmented
```

## 🔧 3. 可复用基础工具

### 3.1 配置管理系统

```python
# 复用: src/utils/config.py
class Config:
    """层次化配置管理"""
    def __init__(self, config_path):
        self.config = self._load_yaml(config_path)
    
    def get_nested_value(self, key_path, default=None):
        """支持 'data.preprocessing.normalize' 格式访问"""
        keys = key_path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value
```

### 3.2 评估指标计算

```python
# 复用: src/evaluation/metrics.py  
class MetricsCalculator:
    """多任务评估指标计算"""
    
    def compute_regression_metrics(self, y_true, y_pred):
        """血管狭窄程度评估"""
        return {
            'mae': np.mean(np.abs(y_true - y_pred)),
            'mse': np.mean((y_true - y_pred) ** 2),
            'r2': r2_score(y_true, y_pred)
        }
    
    def compute_classification_metrics(self, y_true, y_pred_probs):
        """二分类评估（包含敏感度/特异度）"""
        y_pred = (y_pred_probs > 0.5).astype(int)
        return {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred),
            'recall': recall_score(y_true, y_pred),  # 敏感度
            'specificity': self._calculate_specificity(y_true, y_pred),
            'auc': roc_auc_score(y_true, y_pred_probs)
        }
```

### 3.3 数据加载器基类

```python  
# 复用思路: src/data/dataset.py
class BaseMCGDataset(torch.utils.data.Dataset):
    """MCG数据集基类，可继承扩展"""
    
    def __init__(self, data_root, xlsx_file, split='train'):
        self.data_root = Path(data_root)
        self.split = split
        self._load_sample_list(xlsx_file, split)
    
    def _load_sample_list(self, xlsx_file, split):
        """从Excel加载样本列表"""
        # 实现逻辑...
    
    def _load_mcg_signal(self, sample_id):
        """加载单个MCG信号"""
        txt_path = self.data_root / f"{sample_id}.txt"
        return load_mcg_signal(txt_path)
    
    def _process_clinical_features(self, clinical_data):
        """处理临床特征（缺失值、编码等）"""
        # 实现逻辑...
```

## 🏗️ 4. 架构设计参考 (仅供参考)

> ⚠️ **注意**：以下架构建议仅供参考，新项目应根据具体需求自由设计

### 4.1 当前项目的42通道设计背景

**设计背景**：
- 原始36通道：空间信号通道
- 额外6通道：时间点特征（特定医学假设下的扩展）

**重要提醒**：
- 这个42通道设计是针对特定医学假设的探索
- **在三阶段战略项目中可能不需要**，特别是阶段一的基础模型
- MAE预训练更可能只需要原始36通道数据
- 6个时点通道可能只在某些下游微调任务中有价值

### 4.2 时空特征处理参考

```python
# 仅供参考的空间重排方案
def reshape_to_spatial(signal_36ch):
    """36通道 -> 6×6空间网格"""
    # 这是一种可能的排列方案，不是唯一标准
    spatial_map = signal_36ch.reshape(-1, 6, 6)  # [time, 6, 6]
    return spatial_map

# 参考的多尺度处理
def multiscale_spatial_processing(spatial_data):
    """多尺度空间特征提取 - 仅供参考"""
    # CNN + 不同kernel size
    # 或者 ViT patch embeddings
    pass
```

### 4.3 融合策略参考

**后期融合模式**（符合阶段二需求）：
```python
# 参考架构：基础编码器 + 临床MLP分支
class LateFusionModel(nn.Module):
    def __init__(self, foundation_encoder, clinical_dim):
        self.foundation_encoder = foundation_encoder  # 来自阶段一
        self.clinical_branch = nn.Sequential(
            nn.Linear(clinical_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        self.fusion_layer = nn.Linear(encoder_dim + 128, output_dim)
    
    def forward(self, mcg_signal, clinical_features):
        mcg_features = self.foundation_encoder(mcg_signal)
        clinical_features = self.clinical_branch(clinical_features)
        fused = torch.cat([mcg_features, clinical_features], dim=1)
        return self.fusion_layer(fused)
```

## 🚨 5. 避坑指南

### 5.1 数据处理陷阱

**❌ 错误做法**：
```python
# 跨样本归一化导致数据泄露
all_data = np.concatenate(all_samples)
normalized_data = (all_data - all_data.mean()) / all_data.std()
```

**✅ 正确做法**：
```python
# 样本级归一化
for sample in samples:
    normalized_sample = (sample - sample.mean()) / sample.std()
```

### 5.2 配置管理陷阱

**❌ 错误**：混用字典访问和属性访问
```python
# 导致KeyError的错误模式
value = config['data']['preprocessing']  # 如果config是对象会报错
```

**✅ 正确**：统一的配置访问模式
```python
# 使用统一的配置访问接口
value = config.get_nested_value('data.preprocessing', default={})
```

### 5.3 多任务学习陷阱

**关键经验**：
- 不同任务的损失需要仔细平衡权重
- 标签平滑只适用于多分类，不适用于回归任务
- 早停策略需要基于综合指标，不能只看某个单一损失

### 5.4 GPU内存管理

```python
# 处理大规模预训练时的内存问题
def batch_processing_with_gradient_accumulation(model, data_loader, accumulation_steps=4):
    model.train()
    optimizer.zero_grad()
    
    for i, batch in enumerate(data_loader):
        loss = model(batch) / accumulation_steps  # 缩放损失
        loss.backward()
        
        if (i + 1) % accumulation_steps == 0:
            optimizer.step()
            optimizer.zero_grad()
```

## 🎯 6. 针对三阶段战略的特殊建议

### 6.1 阶段一（MAE预训练）数据准备

**数据策略**：
```python
# 整合所有可用MCG数据（有标签+无标签）
def prepare_pretraining_data():
    """
    关键点：
    1. 只使用原始36通道，忽略6个时点通道
    2. 整合所有来源的MCG数据
    3. 统一预处理但保持原始信号特征
    """
    all_mcg_files = collect_all_mcg_files()  # 不区分标签状态
    
    for mcg_file in all_mcg_files:
        signal = load_mcg_signal(mcg_file)  # [n_time, 36]
        # 基础预处理：长度统一 + 样本级归一化
        processed_signal = preprocess_for_mae(signal)
        yield processed_signal
```

**MAE目标任务设计建议**：
- 重建目标：**去噪前的干净信号**
- 掩码策略：时间维度掩码 + 空间维度掩码
- 噪声类型：高斯噪声 + 生理学相关噪声

### 6.2 阶段二（专家模型）复用指导

**可直接复用**：
- 多任务损失函数设计
- 临床特征处理管道  
- 评估指标计算
- 训练循环和早停策略

**需要适配**：
- 模型架构：基础编码器 + 任务头
- 数据加载：支持预训练模型特征提取

### 6.3 阶段三（科学发现）工具需求

**聚类分析工具**：
```python
# 基础聚类分析框架
def patient_clustering_analysis(expert_encoder, unlabeled_data):
    """
    使用专家编码器进行患者聚类
    注意：不是基础编码器，是阶段二训练好的专家编码器
    """
    features = []
    for patient_data in unlabeled_data:
        feature = expert_encoder.encode(patient_data)  # 特征提取
        features.append(feature)
    
    # 多种聚类算法对比
    clustering_results = {
        'kmeans': KMeans(n_clusters=k).fit(features),
        'hierarchical': AgglomerativeClustering().fit(features),
        'dbscan': DBSCAN().fit(features)
    }
    return clustering_results
```
---

**最终提醒**：这份文档旨在传递经验和工具，避免重复踩坑，但**不应该约束新项目的创新和架构自由度**。三阶段战略是宏伟的科学发现项目，需要在这些基础经验上进行大胆的创新探索。