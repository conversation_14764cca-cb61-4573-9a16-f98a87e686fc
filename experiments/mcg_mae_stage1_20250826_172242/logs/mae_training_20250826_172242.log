2025-08-26 17:22:42,471 - __main__ - INFO - 日志系统初始化完成，日志文件: experiments/mcg_mae_stage1_20250826_172242/logs/mae_training_20250826_172242.log
2025-08-26 17:22:42,476 - __main__ - INFO - 实验配置已保存到: experiments/mcg_mae_stage1_20250826_172242/config.yaml
2025-08-26 17:22:42,476 - __main__ - INFO - 实验目录: experiments/mcg_mae_stage1_20250826_172242
2025-08-26 17:22:42,509 - numexpr.utils - INFO - Note: detected 128 virtual cores but NumExpr set to maximum of 64, check "NUMEXPR_MAX_THREADS" environment variable.
2025-08-26 17:22:42,509 - numexpr.utils - INFO - Note: NumExpr detected 128 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-26 17:22:42,509 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-26 17:22:43,477 - __main__ - INFO - 数据路径: /home/<USER>/Wangmeng/data/data0318
2025-08-26 17:22:43,498 - data.dataset - INFO - 发现14376个MCG文件
2025-08-26 17:22:43,630 - data.dataset - INFO - 数据集初始化完成: train, 样本数=14376
2025-08-26 17:22:43,630 - data.dataset - INFO - MAE数据加载器创建完成: batch_size=1, num_workers=0, samples=14376
2025-08-26 17:22:43,651 - data.dataset - INFO - 发现14376个MCG文件
2025-08-26 17:22:43,711 - data.dataset - INFO - 数据集初始化完成: train, 样本数=14376
2025-08-26 17:22:43,712 - training.mae_trainer - INFO - 验证数据加载器创建成功: 20个样本, batch_size=1
2025-08-26 17:22:44,679 - training.mae_trainer - INFO - MAE训练器初始化完成:
2025-08-26 17:22:44,679 - training.mae_trainer - INFO -   - 模型参数量: 2,326,034
2025-08-26 17:22:44,679 - training.mae_trainer - INFO -   - 训练设备: cuda
2025-08-26 17:22:44,679 - training.mae_trainer - INFO -   - 混合精度: True
2025-08-26 17:22:44,679 - training.mae_trainer - INFO -   - 分布式训练: False
2025-08-26 17:22:44,679 - __main__ - INFO - 开始MAE预训练...
2025-08-26 17:22:44,679 - training.mae_trainer - INFO - 开始MAE训练，共2个epoch
2025-08-26 17:22:44,983 - data.preprocessing - WARNING - 检测到异常样本: SHZS_2025_000014
2025-08-26 17:22:46,216 - __main__ - ERROR - 训练过程中出现错误: mat1 and mat2 shapes cannot be multiplied (1x256 and 64x64)
