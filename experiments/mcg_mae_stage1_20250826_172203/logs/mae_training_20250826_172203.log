2025-08-26 17:22:03,543 - __main__ - INFO - 日志系统初始化完成，日志文件: experiments/mcg_mae_stage1_20250826_172203/logs/mae_training_20250826_172203.log
2025-08-26 17:22:03,548 - __main__ - INFO - 实验配置已保存到: experiments/mcg_mae_stage1_20250826_172203/config.yaml
2025-08-26 17:22:03,548 - __main__ - INFO - 实验目录: experiments/mcg_mae_stage1_20250826_172203
2025-08-26 17:22:03,584 - numexpr.utils - INFO - Note: detected 128 virtual cores but NumExpr set to maximum of 64, check "NUMEXPR_MAX_THREADS" environment variable.
2025-08-26 17:22:03,584 - numexpr.utils - INFO - Note: NumExpr detected 128 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-26 17:22:03,584 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-26 17:22:04,549 - __main__ - INFO - 数据路径: /home/<USER>/Wangmeng/data/data0318
2025-08-26 17:22:04,569 - data.dataset - INFO - 发现14376个MCG文件
2025-08-26 17:22:04,702 - data.dataset - INFO - 数据集初始化完成: train, 样本数=14376
2025-08-26 17:22:04,703 - data.dataset - INFO - MAE数据加载器创建完成: batch_size=4, num_workers=0, samples=14376
2025-08-26 17:22:04,723 - data.dataset - INFO - 发现14376个MCG文件
2025-08-26 17:22:04,783 - data.dataset - INFO - 数据集初始化完成: train, 样本数=14376
2025-08-26 17:22:04,783 - training.mae_trainer - INFO - 验证数据加载器创建成功: 20个样本, batch_size=4
2025-08-26 17:22:05,852 - training.mae_trainer - INFO - MAE训练器初始化完成:
2025-08-26 17:22:05,852 - training.mae_trainer - INFO -   - 模型参数量: 21,664,322
2025-08-26 17:22:05,852 - training.mae_trainer - INFO -   - 训练设备: cuda
2025-08-26 17:22:05,852 - training.mae_trainer - INFO -   - 混合精度: True
2025-08-26 17:22:05,852 - training.mae_trainer - INFO -   - 分布式训练: False
2025-08-26 17:22:05,852 - __main__ - INFO - 开始MAE预训练...
2025-08-26 17:22:05,852 - training.mae_trainer - INFO - 开始MAE训练，共2个epoch
2025-08-26 17:22:05,863 - data.preprocessing - WARNING - 检测到异常样本: SHZS_2022_000367
2025-08-26 17:22:05,869 - data.preprocessing - WARNING - 检测到异常样本: GDNH_2025_000279
2025-08-26 17:22:05,875 - data.dataset - ERROR - 预处理失败 PLAG_2024_000538: ufunc 'add' did not contain a loop with signature matching types (dtype('float32'), dtype('<U4')) -> None
2025-08-26 17:22:05,880 - data.preprocessing - WARNING - 检测到异常样本: SY_TT_000509
