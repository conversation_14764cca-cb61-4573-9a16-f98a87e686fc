2025-08-26 17:19:42,632 - __main__ - INFO - 日志系统初始化完成，日志文件: experiments/mcg_mae_stage1_20250826_171942/logs/mae_training_20250826_171942.log
2025-08-26 17:19:42,637 - __main__ - INFO - 实验配置已保存到: experiments/mcg_mae_stage1_20250826_171942/config.yaml
2025-08-26 17:19:42,637 - __main__ - INFO - 实验目录: experiments/mcg_mae_stage1_20250826_171942
2025-08-26 17:19:42,672 - numexpr.utils - INFO - Note: detected 128 virtual cores but NumExpr set to maximum of 64, check "NUMEXPR_MAX_THREADS" environment variable.
2025-08-26 17:19:42,672 - numexpr.utils - INFO - Note: NumExpr detected 128 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-26 17:19:42,672 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-26 17:19:43,646 - __main__ - INFO - 数据路径: /home/<USER>/Wangmeng/data/data0318
2025-08-26 17:19:43,666 - data.dataset - INFO - 发现14376个MCG文件
2025-08-26 17:19:43,801 - data.dataset - INFO - 数据集初始化完成: train, 样本数=14376
2025-08-26 17:19:43,802 - data.dataset - INFO - MAE数据加载器创建完成: batch_size=4, num_workers=0, samples=14376
2025-08-26 17:19:43,822 - data.dataset - INFO - 发现14376个MCG文件
2025-08-26 17:19:43,883 - data.dataset - INFO - 数据集初始化完成: train, 样本数=14376
2025-08-26 17:19:43,883 - training.mae_trainer - INFO - 验证数据加载器创建成功: 20个样本, batch_size=4
2025-08-26 17:19:44,963 - __main__ - ERROR - 训练过程中出现错误: 
get_cosine_schedule_with_warmup requires the PyTorch library but it was not found in your environment. Check out the instructions on the
installation page: https://pytorch.org/get-started/locally/ and follow the ones that match your environment.
Please note that you may need to restart your runtime after installation.

