2025-08-26 17:20:18,374 - __main__ - INFO - 日志系统初始化完成，日志文件: experiments/mcg_mae_stage1_20250826_172018/logs/mae_training_20250826_172018.log
2025-08-26 17:20:18,379 - __main__ - INFO - 实验配置已保存到: experiments/mcg_mae_stage1_20250826_172018/config.yaml
2025-08-26 17:20:18,379 - __main__ - INFO - 实验目录: experiments/mcg_mae_stage1_20250826_172018
2025-08-26 17:20:18,414 - numexpr.utils - INFO - Note: detected 128 virtual cores but NumExpr set to maximum of 64, check "NUMEXPR_MAX_THREADS" environment variable.
2025-08-26 17:20:18,414 - numexpr.utils - INFO - Note: NumExpr detected 128 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-26 17:20:18,414 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-26 17:20:19,391 - __main__ - <PERSON><PERSON>O - 数据路径: /home/<USER>/Wangmeng/data/data0318
2025-08-26 17:20:19,412 - data.dataset - INFO - 发现14376个MCG文件
2025-08-26 17:20:19,547 - data.dataset - INFO - 数据集初始化完成: train, 样本数=14376
2025-08-26 17:20:19,547 - data.dataset - INFO - MAE数据加载器创建完成: batch_size=4, num_workers=0, samples=14376
2025-08-26 17:20:19,568 - data.dataset - INFO - 发现14376个MCG文件
2025-08-26 17:20:19,629 - data.dataset - INFO - 数据集初始化完成: train, 样本数=14376
2025-08-26 17:20:19,629 - training.mae_trainer - INFO - 验证数据加载器创建成功: 20个样本, batch_size=4
2025-08-26 17:20:20,727 - training.mae_trainer - INFO - MAE训练器初始化完成:
2025-08-26 17:20:20,727 - training.mae_trainer - INFO -   - 模型参数量: 21,664,322
2025-08-26 17:20:20,727 - training.mae_trainer - INFO -   - 训练设备: cuda
2025-08-26 17:20:20,727 - training.mae_trainer - INFO -   - 混合精度: True
2025-08-26 17:20:20,727 - training.mae_trainer - INFO -   - 分布式训练: False
2025-08-26 17:20:20,727 - __main__ - INFO - 开始MAE预训练...
2025-08-26 17:20:20,727 - training.mae_trainer - INFO - 开始MAE训练，共2个epoch
2025-08-26 17:20:20,767 - data.preprocessing - WARNING - 检测到异常样本: SHZS_2022_000367
2025-08-26 17:20:20,791 - data.preprocessing - WARNING - 检测到异常样本: GDNH_2025_000279
2025-08-26 17:20:20,802 - data.dataset - ERROR - 预处理失败 PLAG_2024_000538: ufunc 'add' did not contain a loop with signature matching types (dtype('float32'), dtype('<U4')) -> None
2025-08-26 17:20:20,815 - data.preprocessing - WARNING - 检测到异常样本: SY_TT_000509
2025-08-26 17:20:20,816 - __main__ - ERROR - 训练过程中出现错误: not enough values to unpack (expected 3, got 1)
