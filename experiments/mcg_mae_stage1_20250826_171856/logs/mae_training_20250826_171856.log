2025-08-26 17:18:56,879 - __main__ - INFO - 日志系统初始化完成，日志文件: experiments/mcg_mae_stage1_20250826_171856/logs/mae_training_20250826_171856.log
2025-08-26 17:18:56,884 - __main__ - INFO - 实验配置已保存到: experiments/mcg_mae_stage1_20250826_171856/config.yaml
2025-08-26 17:18:56,884 - __main__ - INFO - 实验目录: experiments/mcg_mae_stage1_20250826_171856
2025-08-26 17:18:56,917 - numexpr.utils - INFO - Note: detected 128 virtual cores but NumExpr set to maximum of 64, check "NUMEXPR_MAX_THREADS" environment variable.
2025-08-26 17:18:56,917 - numexpr.utils - INFO - Note: NumExpr detected 128 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-26 17:18:56,917 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-26 17:18:57,869 - __main__ - INFO - 数据路径: /home/<USER>/Wangmeng/data/data0318
2025-08-26 17:18:57,890 - data.dataset - INFO - 发现14376个MCG文件
2025-08-26 17:18:58,025 - data.dataset - INFO - 数据集初始化完成: train, 样本数=14376
2025-08-26 17:18:58,026 - data.dataset - INFO - MAE数据加载器创建完成: batch_size=4, num_workers=0, samples=14376
2025-08-26 17:18:58,046 - data.dataset - INFO - 发现14376个MCG文件
2025-08-26 17:18:58,107 - data.dataset - INFO - 数据集初始化完成: train, 样本数=14376
2025-08-26 17:18:58,107 - training.mae_trainer - INFO - 验证数据加载器创建成功: 20个样本, batch_size=4
