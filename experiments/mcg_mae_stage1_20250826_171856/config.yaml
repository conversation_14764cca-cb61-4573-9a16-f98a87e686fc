augmentation:
  baseline_drift:
    drift_rate_range:
    - 0.001
    - 0.005
    enabled: false
    probability: 0.1
  gaussian_noise:
    enabled: true
    probability: 0.3
    std_range:
    - 0.01
    - 0.05
  powerline_noise:
    amplitude_range:
    - 0.01
    - 0.03
    enabled: false
    frequency:
    - 50
    - 60
    probability: 0.2
  temporal_augmentation:
    time_shift:
      enabled: false
      probability: 0.2
      shift_range:
      - -50
      - 50
    time_stretch:
      enabled: false
      probability: 0.1
      stretch_range:
      - 0.9
      - 1.1
data_config:
  channels: 36
  target_length: 1500
  txt_data_dir: /home/<USER>/Wangmeng/data/data0318
data_split:
  random_seed: 42
  test_ratio: 0.0
  train_ratio: 1.0
  val_ratio: 0.0
dataloader:
  batch_size: 4
  drop_last: true
  num_workers: 0
  pin_memory: true
device: cuda
early_stopping:
  min_delta: 1e-4
  mode: min
  monitor: val_reconstruction_loss
  patience: 15
epochs: 2
eval_interval: 1
experiment_dir: experiments/mcg_mae_stage1_20250826_171856
experiment_name: mcg_mae_stage1_debug
gradient_accumulation_steps: 1
gradient_clip_norm: 1.0
log_interval: 50
mixed_precision: true
model:
  decoder:
    decoder_dim: 256
    encoder_dim: 512
    num_heads: 8
    num_layers: 2
    output_channels: 36
    output_seq_len: 1500
  encoder:
    fusion_module:
      dropout: 0.1
      fusion_type: cross_attention
      hidden_dim: 512
      spatial_dim: 256
      temporal_dim: 256
    hidden_dim: 512
    spatial_encoder:
      conv_channels:
      - 64
      - 128
      - 256
      dropout: 0.1
      input_channels: 36
      pooling: adaptive
      type: mcg_spatial
    temporal_encoder:
      d_model: 256
      dropout: 0.1
      global_pooling: attention
      nhead: 8
      num_layers: 4
      type: mcg_temporal
  loss_type: mse
  mask_loss_only: true
  masking:
    block_size_range:
    - 20
    - 100
    mask_ratio: 0.2
    type: temporal_block
  normalize_target: true
optimizer:
  betas:
  - 0.9
  - 0.95
  lr: 0.0001
  type: AdamW
  weight_decay: 0.05
preprocessing:
  normalization:
    method: standard
    params:
      clip_range:
      - -10
      - 10
      eps: 1e-8
      quantile_range:
      - 0.05
      - 0.95
    strategy: sample_level
  padding:
    method: reflect
    target_length: 1500
    truncate_mode: center
  quality_control:
    max_length: 3000
    min_length: 300
    nan_threshold: 0.05
    outlier_detection:
      enabled: true
      method: iqr
      threshold: 3.0
random_seed: 42
save_interval: 1
scheduler:
  enabled: true
  type: cosine_with_warmup
  warmup_ratio: 0.1
validation:
  enabled: true
