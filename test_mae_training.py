#!/usr/bin/env python3
"""
MAE训练流程测试
==============

测试完整的MAE训练流程：
1. 模型创建
2. 数据加载器创建（模拟数据）
3. 训练器创建
4. 短期训练测试
"""

import torch
import numpy as np
import sys
import tempfile
import shutil
from pathlib import Path

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def create_mock_data(data_dir: Path, num_samples: int = 10):
    """创建模拟MCG数据文件"""
    data_dir.mkdir(parents=True, exist_ok=True)
    
    for i in range(num_samples):
        # 模拟MCG数据：1500个时间点，36个通道
        seq_len = np.random.randint(800, 1200)  # 变长序列
        data = np.random.randn(seq_len, 37)  # 第一列是索引
        data[:, 0] = np.arange(seq_len)  # 时间索引
        
        # 保存为txt文件
        file_path = data_dir / f"mcg_sample_{i:03d}.txt"
        np.savetxt(file_path, data, delimiter='\t', fmt='%.6f')
    
    print(f"✅ 创建了{num_samples}个模拟MCG文件")

def test_mae_training():
    """测试MAE训练流程"""
    print("=== 测试MAE训练流程 ===")
    
    # 创建临时目录
    temp_dir = Path(tempfile.mkdtemp())
    data_dir = temp_dir / "data"
    exp_dir = temp_dir / "experiments"
    
    try:
        # 1. 创建模拟数据
        create_mock_data(data_dir, num_samples=5)
        
        # 2. 创建配置
        config = {
            # 模型配置
            'model': {
                'encoder': {
                    'hidden_dim': 64,
                    'spatial_encoder': {
                        'type': 'mcg_spatial', 
                        'conv_channels': [8, 16], 
                        'dropout': 0.0, 
                        'pooling': 'adaptive', 
                        'use_attention': False
                    },
                    'temporal_encoder': {
                        'type': 'mcg_temporal', 
                        'd_model': 64, 
                        'nhead': 2, 
                        'num_layers': 1, 
                        'dropout': 0.0,
                        'global_pooling': 'attention'
                    },
                    'fusion_module': {
                        'fusion_type': 'cross_attention',
                        'spatial_dim': 256,
                        'temporal_dim': 64,
                        'hidden_dim': 64, 
                        'dropout': 0.0
                    }
                },
                'decoder': {
                    'encoder_dim': 64,
                    'decoder_dim': 32,
                    'num_heads': 2,
                    'num_layers': 1,
                    'output_channels': 36,
                    'output_seq_len': 1000
                },
                'masking': {
                    'type': 'temporal_block', 
                    'mask_ratio': 0.2, 
                    'block_size_range': [10, 30]
                },
                'loss_type': 'mse',
                'normalize_target': True,
                'mask_loss_only': True
            },
            
            # 数据配置
            'data_config': {
                'txt_data_dir': str(data_dir),
                'target_length': 1000,
                'channels': 36
            },
            'data_split': {
                'train_ratio': 1.0,
                'val_ratio': 0.0,
                'random_seed': 42
            },
            'preprocessing': {
                'normalization': {
                    'strategy': 'sample_level',
                    'method': 'standard',
                    'params': {'eps': 1e-8}
                },
                'padding': {
                    'target_length': 1000,
                    'method': 'reflect',
                    'truncate_mode': 'center'
                },
                'quality_control': {
                    'nan_threshold': 0.1,
                    'min_length': 500,
                    'max_length': 2000,
                    'outlier_detection': {'enabled': False}
                }
            },
            'augmentation': {
                'gaussian_noise': {
                    'enabled': True,
                    'probability': 0.3,
                    'std_range': [0.01, 0.05]
                }
            },
            
            # 训练配置
            'epochs': 2,  # 只训练2个epoch
            'optimizer': {
                'type': 'AdamW',
                'lr': 1e-4,
                'weight_decay': 0.01
            },
            'scheduler': {
                'enabled': False  # 简化配置
            },
            'dataloader': {
                'batch_size': 2,
                'num_workers': 0,
                'pin_memory': False,
                'drop_last': False
            },
            'validation': {
                'enabled': True
            },
            'mixed_precision': False,  # 简化配置
            'gradient_clip_norm': 1.0,
            'eval_interval': 1,
            'save_interval': 1,
            'log_interval': 1,
            'early_stopping': {
                'patience': 10,
                'min_delta': 1e-4
            },
            'experiment_dir': str(exp_dir)
        }
        
        # 3. 创建模型和数据加载器（简化版本）
        from models.mae.mae_model import create_mae_model
        from data.dataset import create_mae_dataloader

        model = create_mae_model(config['model'])
        train_dataloader = create_mae_dataloader(data_dir, config)

        print(f"✅ 模型和数据加载器创建成功")
        print(f"  - 训练样本数: {len(train_dataloader.dataset)}")
        print(f"  - 模型参数量: {sum(p.numel() for p in model.parameters()):,}")

        # 4. 简单的训练循环测试
        model.train()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)

        print("开始简单训练测试...")

        for epoch in range(2):
            epoch_loss = 0.0
            num_batches = 0

            for batch_idx, batch in enumerate(train_dataloader):
                if batch_idx >= 2:  # 只测试前2个批次
                    break

                signals = batch['signal']
                targets = batch['target']

                optimizer.zero_grad()

                # 前向传播
                result = model(targets)
                loss = result['total_loss']

                # 反向传播
                loss.backward()
                optimizer.step()

                epoch_loss += loss.item()
                num_batches += 1

                print(f"  Epoch {epoch+1}, Batch {batch_idx+1}: Loss = {loss.item():.6f}")

            avg_loss = epoch_loss / num_batches if num_batches > 0 else 0
            print(f"  Epoch {epoch+1} 平均损失: {avg_loss:.6f}")

        print("✅ 简单训练测试完成")
        


        
        return True
        
    except Exception as e:
        print(f"❌ 训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时文件
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print(f"🧹 清理临时目录: {temp_dir}")

def main():
    """主函数"""
    print("MAE训练流程测试")
    print("=" * 50)
    
    success = test_mae_training()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 MAE训练流程测试通过！")
        print("Phase 1 (MAE预训练) 已准备就绪")
    else:
        print("❌ MAE训练流程测试失败")
        print("需要进一步调试")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
