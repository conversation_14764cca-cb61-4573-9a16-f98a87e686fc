"""
MAE训练器
=========

为MCG MAE预训练提供完整的训练和评估功能：
- 分布式训练支持
- 混合精度训练
- 学习率调度
- 早停机制
- 实验追踪和可视化
- 检查点管理
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP

import numpy as np
import time
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
import json
from tqdm import tqdm
import warnings

# 项目导入
from ..models.mae.mae_model import create_mae_model
from ..data.dataset import create_mae_dataloader
from ..evaluation.metrics import MetricsAggregator, compute_mae_metrics
from ..evaluation.visualizer import MAEVisualizer, MAEExperimentVisualizer

logger = logging.getLogger(__name__)


class MAETrainer:
    """MAE训练器 - 完整的训练管理"""
    
    def __init__(self, config: Dict[str, Any], 
                 train_dataloader: DataLoader,
                 val_dataloader: Optional[DataLoader] = None,
                 device: Optional[torch.device] = None):
        """
        初始化训练器
        
        Args:
            config: 训练配置
            train_dataloader: 训练数据加载器
            val_dataloader: 验证数据加载器 (可选)
            device: 计算设备
        """
        self.config = config
        self.train_dataloader = train_dataloader
        self.val_dataloader = val_dataloader
        
        # 设备配置
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.use_mixed_precision = config.get('mixed_precision', True) and self.device.type == 'cuda'
        
        # 分布式训练
        self.distributed = config.get('distributed', False)
        self.local_rank = config.get('local_rank', 0)
        
        # 创建模型
        model_config = config.get('model', {})
        self.model = create_mae_model(model_config)
        self.model.to(self.device)
        
        # 分布式包装
        if self.distributed:
            self.model = DDP(self.model, device_ids=[self.local_rank])
            self.model_module = self.model.module
        else:
            self.model_module = self.model
        
        # 优化器配置
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
        
        # 混合精度
        self.scaler = GradScaler() if self.use_mixed_precision else None
        
        # 训练状态
        self.current_epoch = 0
        self.global_step = 0
        self.best_loss = float('inf')
        self.best_metrics = {}
        
        # 评估和可视化
        self.metrics_aggregator = MetricsAggregator()
        self.train_losses = []
        self.val_losses = []
        self.train_metrics = []
        self.val_metrics = []
        
        # 实验追踪
        self.experiment_dir = Path(config.get('experiment_dir', 'experiments/mae_pretrain'))
        self.experiment_dir.mkdir(parents=True, exist_ok=True)
        
        self.checkpoint_dir = self.experiment_dir / 'checkpoints'
        self.checkpoint_dir.mkdir(exist_ok=True)
        
        self.log_dir = self.experiment_dir / 'logs'
        self.log_dir.mkdir(exist_ok=True)
        
        # 可视化器
        self.experiment_visualizer = MAEExperimentVisualizer(self.experiment_dir)
        
        # 训练配置
        self.epochs = config.get('epochs', 100)
        self.gradient_clip_norm = config.get('gradient_clip_norm', 1.0)
        self.gradient_accumulation_steps = config.get('gradient_accumulation_steps', 1)
        self.eval_interval = config.get('eval_interval', 5)
        self.save_interval = config.get('save_interval', 10)
        self.log_interval = config.get('log_interval', 100)
        
        # 早停配置
        early_stop_config = config.get('early_stopping', {})
        self.early_stop_patience = early_stop_config.get('patience', 15)
        self.early_stop_min_delta = early_stop_config.get('min_delta', 1e-4)
        self.patience_counter = 0
        
        logger.info(f"MAE训练器初始化完成:")
        logger.info(f"  - 模型参数量: {sum(p.numel() for p in self.model.parameters()):,}")
        logger.info(f"  - 训练设备: {self.device}")
        logger.info(f"  - 混合精度: {self.use_mixed_precision}")
        logger.info(f"  - 分布式训练: {self.distributed}")
    
    def _create_optimizer(self) -> torch.optim.Optimizer:
        """创建优化器"""
        optimizer_config = self.config.get('optimizer', {})
        optimizer_type = optimizer_config.get('type', 'AdamW')
        
        # 参数分组 (可选的权重衰减策略)
        no_decay = ['bias', 'LayerNorm.weight', 'layernorm.weight', 'norm.weight']
        param_groups = [
            {
                'params': [p for n, p in self.model.named_parameters() 
                          if not any(nd in n for nd in no_decay)],
                'weight_decay': optimizer_config.get('weight_decay', 0.05)
            },
            {
                'params': [p for n, p in self.model.named_parameters() 
                          if any(nd in n for nd in no_decay)],
                'weight_decay': 0.0
            }
        ]
        
        if optimizer_type == 'AdamW':
            optimizer = optim.AdamW(
                param_groups,
                lr=optimizer_config.get('lr', 1e-4),
                betas=optimizer_config.get('betas', [0.9, 0.95]),
                eps=optimizer_config.get('eps', 1e-8)
            )
        elif optimizer_type == 'Adam':
            optimizer = optim.Adam(
                param_groups,
                lr=optimizer_config.get('lr', 1e-4),
                betas=optimizer_config.get('betas', [0.9, 0.999])
            )
        elif optimizer_type == 'SGD':
            optimizer = optim.SGD(
                param_groups,
                lr=optimizer_config.get('lr', 1e-3),
                momentum=optimizer_config.get('momentum', 0.9)
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")
        
        return optimizer
    
    def _create_scheduler(self) -> Optional[torch.optim.lr_scheduler._LRScheduler]:
        """创建学习率调度器"""
        scheduler_config = self.config.get('scheduler', {})
        if not scheduler_config.get('enabled', True):
            return None
        
        scheduler_type = scheduler_config.get('type', 'cosine_with_warmup')
        
        if scheduler_type == 'cosine_with_warmup':
            from transformers import get_cosine_schedule_with_warmup
            
            total_steps = len(self.train_dataloader) * self.epochs // self.gradient_accumulation_steps
            warmup_steps = int(total_steps * scheduler_config.get('warmup_ratio', 0.1))
            
            scheduler = get_cosine_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
                num_training_steps=total_steps
            )
        elif scheduler_type == 'cosine':
            scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.epochs,
                eta_min=scheduler_config.get('min_lr', 1e-6)
            )
        elif scheduler_type == 'step':
            scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=scheduler_config.get('step_size', 30),
                gamma=scheduler_config.get('gamma', 0.1)
            )
        elif scheduler_type == 'exponential':
            scheduler = optim.lr_scheduler.ExponentialLR(
                self.optimizer,
                gamma=scheduler_config.get('gamma', 0.95)
            )
        else:
            scheduler = None
        
        return scheduler
    
    def train_epoch(self) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        total_steps = 0
        
        # 设置掩码策略的当前轮数 (渐进式掩码)
        if hasattr(self.model_module, 'set_masking_epoch'):
            self.model_module.set_masking_epoch(self.current_epoch)
        
        progress_bar = tqdm(
            self.train_dataloader, 
            desc=f'Epoch {self.current_epoch}',
            disable=self.distributed and self.local_rank != 0
        )
        
        self.optimizer.zero_grad()
        
        for step, batch in enumerate(progress_bar):
            # 数据移动到设备
            signals = batch['signal'].to(self.device, non_blocking=True)
            targets = batch['target'].to(self.device, non_blocking=True)
            
            # 前向传播 (支持混合精度)
            with autocast(enabled=self.use_mixed_precision):
                result = self.model(targets)  # 使用干净信号作为目标
                loss = result['total_loss']
                
                # 梯度累积
                if self.gradient_accumulation_steps > 1:
                    loss = loss / self.gradient_accumulation_steps
            
            # 反向传播
            if self.use_mixed_precision:
                self.scaler.scale(loss).backward()
            else:
                loss.backward()
            
            # 梯度更新
            if (step + 1) % self.gradient_accumulation_steps == 0:
                # 梯度裁剪
                if self.gradient_clip_norm > 0:
                    if self.use_mixed_precision:
                        self.scaler.unscale_(self.optimizer)
                    
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(), 
                        self.gradient_clip_norm
                    )
                
                # 优化器步骤
                if self.use_mixed_precision:
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                else:
                    self.optimizer.step()
                
                # 学习率调度
                if self.scheduler is not None and self.config.get('scheduler', {}).get('type') == 'cosine_with_warmup':
                    self.scheduler.step()
                
                self.optimizer.zero_grad()
                self.global_step += 1
            
            # 累积损失
            total_loss += loss.item() * self.gradient_accumulation_steps
            total_steps += 1
            
            # 更新进度条
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'lr': f'{self.optimizer.param_groups[0]["lr"]:.2e}'
            })
            
            # 定期日志
            if self.global_step % self.log_interval == 0:
                self._log_training_step(loss.item(), result)
        
        # Epoch结束后的学习率调度
        if self.scheduler is not None and self.config.get('scheduler', {}).get('type') != 'cosine_with_warmup':
            self.scheduler.step()
        
        avg_loss = total_loss / total_steps if total_steps > 0 else 0.0
        
        return {
            'train_loss': avg_loss,
            'learning_rate': self.optimizer.param_groups[0]['lr'],
            'global_step': self.global_step
        }
    
    def validate(self) -> Dict[str, float]:
        """验证模型"""
        if self.val_dataloader is None:
            return {}
        
        self.model.eval()
        
        total_loss = 0.0
        total_steps = 0
        sample_results = []
        
        with torch.no_grad():
            for batch in tqdm(self.val_dataloader, desc='Validation', leave=False):
                signals = batch['signal'].to(self.device, non_blocking=True)
                targets = batch['target'].to(self.device, non_blocking=True)
                
                # 前向传播
                with autocast(enabled=self.use_mixed_precision):
                    result = self.model(targets, return_encoder_output=True)
                    loss = result['total_loss']
                
                total_loss += loss.item()
                total_steps += 1
                
                # 收集样本结果 (前几个批次)
                if len(sample_results) < 5:
                    sample_results.append({
                        'original': targets[0].cpu().numpy(),
                        'reconstructed': result['reconstructed'][0].cpu().numpy(),
                        'mask': result['mask'][0].cpu().numpy()
                    })
                
                # 更新指标
                self.metrics_aggregator.update_mae_metrics(
                    targets, result['reconstructed'], result['mask']
                )
        
        # 计算平均指标
        avg_loss = total_loss / total_steps if total_steps > 0 else 0.0
        epoch_metrics = self.metrics_aggregator.compute_epoch_metrics(self.current_epoch)
        
        val_result = {
            'val_loss': avg_loss,
            'sample_results': sample_results,
            **epoch_metrics
        }
        
        return val_result
    
    def train(self):
        """主训练循环"""
        logger.info(f"开始MAE训练，共{self.epochs}个epoch")
        
        start_time = time.time()
        
        for epoch in range(self.epochs):
            self.current_epoch = epoch
            
            # 训练
            train_metrics = self.train_epoch()
            self.train_losses.append(train_metrics['train_loss'])
            self.train_metrics.append(train_metrics)
            
            # 验证
            if epoch % self.eval_interval == 0 or epoch == self.epochs - 1:
                val_metrics = self.validate()
                
                if val_metrics:
                    self.val_losses.append(val_metrics['val_loss'])
                    self.val_metrics.append(val_metrics)
                    
                    # 早停检查
                    current_loss = val_metrics['val_loss']
                    if current_loss < self.best_loss - self.early_stop_min_delta:
                        self.best_loss = current_loss
                        self.best_metrics = val_metrics.copy()
                        self.patience_counter = 0
                        
                        # 保存最佳模型
                        self.save_checkpoint('best_model.pth', is_best=True)
                    else:
                        self.patience_counter += 1
                    
                    # 记录日志
                    self._log_epoch(epoch, train_metrics, val_metrics)
                    
                    # 早停
                    if self.patience_counter >= self.early_stop_patience:
                        logger.info(f"早停触发，patience={self.patience_counter}")
                        break
                else:
                    self._log_epoch(epoch, train_metrics, {})
            
            # 定期保存检查点
            if epoch % self.save_interval == 0 or epoch == self.epochs - 1:
                self.save_checkpoint(f'epoch_{epoch}.pth')
        
        # 训练结束
        total_time = time.time() - start_time
        logger.info(f"训练完成，总用时: {total_time/3600:.2f}小时")
        
        # 生成最终报告
        self._generate_final_report()
    
    def save_checkpoint(self, filename: str, is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'global_step': self.global_step,
            'model_state_dict': self.model_module.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'scaler_state_dict': self.scaler.state_dict() if self.scaler else None,
            'best_loss': self.best_loss,
            'config': self.config,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses
        }
        
        save_path = self.checkpoint_dir / filename
        torch.save(checkpoint, save_path)
        
        if is_best:
            # 同时保存为best模型
            best_path = self.checkpoint_dir / 'best_model.pth'
            torch.save(checkpoint, best_path)
        
        logger.info(f"检查点已保存: {save_path}")
    
    def load_checkpoint(self, checkpoint_path: Union[str, Path], resume_training: bool = True):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        # 加载模型权重
        self.model_module.load_state_dict(checkpoint['model_state_dict'])
        
        if resume_training:
            # 恢复训练状态
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            
            if checkpoint['scheduler_state_dict'] and self.scheduler:
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            
            if checkpoint['scaler_state_dict'] and self.scaler:
                self.scaler.load_state_dict(checkpoint['scaler_state_dict'])
            
            self.current_epoch = checkpoint['epoch']
            self.global_step = checkpoint['global_step']
            self.best_loss = checkpoint['best_loss']
            self.train_losses = checkpoint.get('train_losses', [])
            self.val_losses = checkpoint.get('val_losses', [])
        
        logger.info(f"检查点已加载: {checkpoint_path}")
    
    def _log_training_step(self, loss: float, result: Dict[str, torch.Tensor]):
        """记录训练步骤"""
        # 这里可以添加tensorboard或wandb的记录
        pass
    
    def _log_epoch(self, epoch: int, train_metrics: Dict, val_metrics: Dict):
        """记录epoch信息"""
        log_info = f"Epoch {epoch:3d}"
        log_info += f" | Train Loss: {train_metrics['train_loss']:.4f}"
        log_info += f" | LR: {train_metrics['learning_rate']:.2e}"
        
        if val_metrics:
            log_info += f" | Val Loss: {val_metrics['val_loss']:.4f}"
            
            # 添加MAE特定指标
            for key, value in val_metrics.items():
                if key.startswith('mae_') and isinstance(value, float):
                    log_info += f" | {key}: {value:.4f}"
        
        logger.info(log_info)
    
    def _generate_final_report(self):
        """生成最终实验报告"""
        # 准备实验数据
        experiment_data = {
            'config': self.config,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_metrics': self.train_metrics,
            'val_metrics': self.val_metrics,
            'best_metrics': self.best_metrics,
            'total_epochs': self.current_epoch + 1,
            'final_model_path': str(self.checkpoint_dir / 'best_model.pth')
        }
        
        # 生成可视化报告
        self.experiment_visualizer.create_experiment_report(experiment_data)
        
        # 保存实验配置和结果
        results_path = self.experiment_dir / 'experiment_results.json'
        with open(results_path, 'w') as f:
            # 过滤掉不能JSON序列化的对象
            json_safe_data = {}
            for key, value in experiment_data.items():
                if key in ['config', 'best_metrics', 'total_epochs', 'final_model_path']:
                    json_safe_data[key] = value
                elif key in ['train_losses', 'val_losses']:
                    json_safe_data[key] = value
            
            json.dump(json_safe_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"实验报告已生成: {self.experiment_dir}")


def create_mae_trainer(config: Dict[str, Any], 
                      data_root: Union[str, Path],
                      device: Optional[torch.device] = None) -> MAETrainer:
    """
    创建MAE训练器的便利函数
    
    Args:
        config: 完整的训练配置
        data_root: 数据根目录
        device: 计算设备
        
    Returns:
        配置好的训练器
    """
    # 创建数据加载器
    dataloader_config = config.get('dataloader', {})
    train_dataloader = create_mae_dataloader(data_root, config, **dataloader_config)
    
    # 验证数据加载器 (如果需要)
    val_dataloader = None
    if config.get('validation', {}).get('enabled', False):
        val_config = config.copy()
        val_config['data_split']['train_ratio'] = 0.8
        val_config['data_split']['val_ratio'] = 0.2
        # 这里可以实现验证数据加载器的创建逻辑
    
    # 创建训练器
    trainer = MAETrainer(config, train_dataloader, val_dataloader, device)
    
    return trainer


if __name__ == "__main__":
    # 测试训练器
    test_config = {
        'model': {
            'encoder': {'hidden_dim': 256},
            'decoder': {'decoder_dim': 128}, 
            'masking': {'type': 'temporal_block', 'mask_ratio': 0.2}
        },
        'optimizer': {'type': 'AdamW', 'lr': 1e-4},
        'scheduler': {'type': 'cosine_with_warmup', 'warmup_ratio': 0.1},
        'epochs': 5,
        'mixed_precision': True,
        'gradient_clip_norm': 1.0,
        'eval_interval': 2,
        'save_interval': 2,
        'dataloader': {'batch_size': 4, 'num_workers': 0}
    }
    
    # 模拟数据路径
    data_root = "/tmp/test_data"
    
    print("MAE训练器测试配置:")
    print(f"  - 模型配置: {test_config['model']}")
    print(f"  - 优化器: {test_config['optimizer']}")
    print(f"  - 训练轮数: {test_config['epochs']}")
    
    # 如果有真实数据，可以创建并测试训练器
    # trainer = create_mae_trainer(test_config, data_root)
    # trainer.train()
    
    print("MAE训练器代码实现完成！")