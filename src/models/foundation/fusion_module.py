"""
时空融合模块实现
================

结合空间特征和时间特征，支持多种融合策略：
- 简单拼接 (Concatenation)
- 元素级相加 (Element-wise Addition)  
- 交叉注意力融合 (Cross-Attention)
- 门控融合 (Gated Fusion)
- 双线性融合 (Bilinear Fusion)

所有策略都可通过配置灵活选择和组合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, List, Any
import math

from ..interfaces.base_encoder import FusionModule, EncoderRegistry


class CrossAttentionFusion(FusionModule):
    """交叉注意力融合 - 让空间和时间特征相互关注"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        self.spatial_dim = config.get('spatial_dim', 256)
        self.temporal_dim = config.get('temporal_dim', 256)
        self.hidden_dim = config.get('hidden_dim', 512)
        self.num_heads = config.get('num_heads', 8)
        self.dropout = config.get('dropout', 0.1)
        
        # 维度对齐
        if self.spatial_dim != self.temporal_dim:
            self.spatial_proj = nn.Linear(self.spatial_dim, self.hidden_dim)
            self.temporal_proj = nn.Linear(self.temporal_dim, self.hidden_dim)
            self.feature_dim = self.hidden_dim
        else:
            self.spatial_proj = nn.Identity()
            self.temporal_proj = nn.Identity()
            self.feature_dim = self.spatial_dim
        
        # 交叉注意力层
        self.spatial_to_temporal = nn.MultiheadAttention(
            self.feature_dim, self.num_heads, dropout=self.dropout, batch_first=True
        )
        self.temporal_to_spatial = nn.MultiheadAttention(
            self.feature_dim, self.num_heads, dropout=self.dropout, batch_first=True
        )
        
        # 层归一化
        self.norm1 = nn.LayerNorm(self.feature_dim)
        self.norm2 = nn.LayerNorm(self.feature_dim)
        
        # 最终融合
        self.final_fusion = nn.Sequential(
            nn.Linear(self.feature_dim * 2, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim, self.hidden_dim)
        )
        
        self.output_dim = self.hidden_dim
    
    def get_output_dim(self) -> int:
        return self.output_dim
    
    def forward(self, spatial_features: torch.Tensor, 
                temporal_features: torch.Tensor) -> torch.Tensor:
        """
        交叉注意力融合
        
        Args:
            spatial_features: 空间特征 [batch, spatial_dim]
            temporal_features: 时间特征 [batch, temporal_dim]
            
        Returns:
            融合特征 [batch, output_dim]
        """
        batch_size = spatial_features.shape[0]
        
        # 维度对齐
        spatial_aligned = self.spatial_proj(spatial_features).unsqueeze(1)  # [batch, 1, feature_dim]
        temporal_aligned = self.temporal_proj(temporal_features).unsqueeze(1)  # [batch, 1, feature_dim]
        
        # 交叉注意力
        # 空间特征关注时间特征
        spatial_enhanced, _ = self.spatial_to_temporal(
            spatial_aligned, temporal_aligned, temporal_aligned
        )
        spatial_enhanced = self.norm1(spatial_aligned + spatial_enhanced)
        
        # 时间特征关注空间特征  
        temporal_enhanced, _ = self.temporal_to_spatial(
            temporal_aligned, spatial_aligned, spatial_aligned
        )
        temporal_enhanced = self.norm2(temporal_aligned + temporal_enhanced)
        
        # 拼接并融合
        combined = torch.cat([
            spatial_enhanced.squeeze(1), 
            temporal_enhanced.squeeze(1)
        ], dim=1)  # [batch, feature_dim * 2]
        
        output = self.final_fusion(combined)
        return output


class GatedFusion(FusionModule):
    """门控融合 - 学习空间和时间特征的权重"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        self.spatial_dim = config.get('spatial_dim', 256)
        self.temporal_dim = config.get('temporal_dim', 256)
        self.hidden_dim = config.get('hidden_dim', 512)
        self.dropout = config.get('dropout', 0.1)
        
        # 特征投影
        self.spatial_proj = nn.Linear(self.spatial_dim, self.hidden_dim)
        self.temporal_proj = nn.Linear(self.temporal_dim, self.hidden_dim)
        
        # 门控机制
        self.gate = nn.Sequential(
            nn.Linear(self.spatial_dim + self.temporal_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, 2),  # 2个门：spatial_gate, temporal_gate
            nn.Softmax(dim=1)
        )
        
        # 最终投影
        self.output_proj = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim, self.hidden_dim)
        )
        
        self.output_dim = self.hidden_dim
    
    def get_output_dim(self) -> int:
        return self.output_dim
    
    def forward(self, spatial_features: torch.Tensor, 
                temporal_features: torch.Tensor) -> torch.Tensor:
        """
        门控融合
        
        Args:
            spatial_features: 空间特征 [batch, spatial_dim]
            temporal_features: 时间特征 [batch, temporal_dim]
            
        Returns:
            融合特征 [batch, output_dim]
        """
        # 特征投影
        spatial_proj = self.spatial_proj(spatial_features)  # [batch, hidden_dim]
        temporal_proj = self.temporal_proj(temporal_features)  # [batch, hidden_dim]
        
        # 计算门控权重
        combined = torch.cat([spatial_features, temporal_features], dim=1)
        gates = self.gate(combined)  # [batch, 2]
        spatial_gate = gates[:, 0:1]  # [batch, 1]
        temporal_gate = gates[:, 1:2]  # [batch, 1]
        
        # 门控融合
        fused = spatial_gate * spatial_proj + temporal_gate * temporal_proj
        
        # 最终投影
        output = self.output_proj(fused)
        return output


class BilinearFusion(FusionModule):
    """双线性融合 - 捕获特征间的交互"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        self.spatial_dim = config.get('spatial_dim', 256)
        self.temporal_dim = config.get('temporal_dim', 256)
        self.hidden_dim = config.get('hidden_dim', 512)
        self.dropout = config.get('dropout', 0.1)
        
        # 双线性层
        self.bilinear = nn.Bilinear(self.spatial_dim, self.temporal_dim, self.hidden_dim)
        
        # 残差连接的投影层
        self.spatial_proj = nn.Linear(self.spatial_dim, self.hidden_dim)
        self.temporal_proj = nn.Linear(self.temporal_dim, self.hidden_dim)
        
        # 最终处理
        self.final_layer = nn.Sequential(
            nn.LayerNorm(self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim, self.hidden_dim)
        )
        
        self.output_dim = self.hidden_dim
    
    def get_output_dim(self) -> int:
        return self.output_dim
    
    def forward(self, spatial_features: torch.Tensor, 
                temporal_features: torch.Tensor) -> torch.Tensor:
        """
        双线性融合
        
        Args:
            spatial_features: 空间特征 [batch, spatial_dim]
            temporal_features: 时间特征 [batch, temporal_dim]
            
        Returns:
            融合特征 [batch, output_dim]
        """
        # 双线性交互
        bilinear_output = self.bilinear(spatial_features, temporal_features)
        
        # 残差连接
        spatial_residual = self.spatial_proj(spatial_features)
        temporal_residual = self.temporal_proj(temporal_features)
        
        # 组合
        combined = bilinear_output + spatial_residual + temporal_residual
        
        # 最终处理
        output = self.final_layer(combined)
        return output


class SimpleFusion(FusionModule):
    """简单融合策略 - 拼接或相加"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        self.spatial_dim = config.get('spatial_dim', 256)
        self.temporal_dim = config.get('temporal_dim', 256)
        self.fusion_type = config.get('fusion_type', 'concat')  # ['concat', 'add', 'multiply']
        self.hidden_dim = config.get('hidden_dim', 512)
        self.dropout = config.get('dropout', 0.1)
        
        if self.fusion_type == 'concat':
            # 拼接融合
            self.fusion_layer = nn.Sequential(
                nn.Linear(self.spatial_dim + self.temporal_dim, self.hidden_dim),
                nn.ReLU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.hidden_dim, self.hidden_dim)
            )
            self.output_dim = self.hidden_dim
            
        elif self.fusion_type in ['add', 'multiply']:
            # 元素级融合 - 需要维度对齐
            if self.spatial_dim != self.temporal_dim:
                self.spatial_proj = nn.Linear(self.spatial_dim, self.hidden_dim)
                self.temporal_proj = nn.Linear(self.temporal_dim, self.hidden_dim)
            else:
                self.spatial_proj = nn.Identity()
                self.temporal_proj = nn.Identity()
                self.hidden_dim = self.spatial_dim
            
            self.post_fusion = nn.Sequential(
                nn.LayerNorm(self.hidden_dim),
                nn.ReLU(),
                nn.Dropout(self.dropout)
            )
            self.output_dim = self.hidden_dim
    
    def get_output_dim(self) -> int:
        return self.output_dim
    
    def forward(self, spatial_features: torch.Tensor, 
                temporal_features: torch.Tensor) -> torch.Tensor:
        """
        简单融合
        
        Args:
            spatial_features: 空间特征 [batch, spatial_dim]
            temporal_features: 时间特征 [batch, temporal_dim]
            
        Returns:
            融合特征 [batch, output_dim]
        """
        if self.fusion_type == 'concat':
            # 拼接融合
            combined = torch.cat([spatial_features, temporal_features], dim=1)
            output = self.fusion_layer(combined)
            
        elif self.fusion_type == 'add':
            # 相加融合
            spatial_aligned = self.spatial_proj(spatial_features)
            temporal_aligned = self.temporal_proj(temporal_features)
            combined = spatial_aligned + temporal_aligned
            output = self.post_fusion(combined)
            
        elif self.fusion_type == 'multiply':
            # 元素级相乘
            spatial_aligned = self.spatial_proj(spatial_features)
            temporal_aligned = self.temporal_proj(temporal_features)
            combined = spatial_aligned * temporal_aligned
            output = self.post_fusion(combined)
            
        else:
            raise ValueError(f"不支持的融合类型: {self.fusion_type}")
        
        return output


class AdaptiveFusion(FusionModule):
    """自适应融合 - 根据输入特征动态选择融合策略"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        self.spatial_dim = config.get('spatial_dim', 256)
        self.temporal_dim = config.get('temporal_dim', 256)
        self.hidden_dim = config.get('hidden_dim', 512)
        self.num_strategies = config.get('num_strategies', 3)  # 融合策略数量
        self.dropout = config.get('dropout', 0.1)
        
        # 多种融合策略
        self.fusion_strategies = nn.ModuleList([
            SimpleFusion({**config, 'fusion_type': 'concat'}),
            GatedFusion(config),
            BilinearFusion(config)
        ][:self.num_strategies])
        
        # 策略选择器
        self.strategy_selector = nn.Sequential(
            nn.Linear(self.spatial_dim + self.temporal_dim, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(self.hidden_dim // 2, self.num_strategies),
            nn.Softmax(dim=1)
        )
        
        self.output_dim = self.hidden_dim
    
    def get_output_dim(self) -> int:
        return self.output_dim
    
    def forward(self, spatial_features: torch.Tensor, 
                temporal_features: torch.Tensor) -> torch.Tensor:
        """
        自适应融合
        
        Args:
            spatial_features: 空间特征 [batch, spatial_dim]
            temporal_features: 时间特征 [batch, temporal_dim]
            
        Returns:
            融合特征 [batch, output_dim]
        """
        # 计算策略权重
        combined_input = torch.cat([spatial_features, temporal_features], dim=1)
        strategy_weights = self.strategy_selector(combined_input)  # [batch, num_strategies]
        
        # 应用所有融合策略
        strategy_outputs = []
        for strategy in self.fusion_strategies:
            output = strategy(spatial_features, temporal_features)
            strategy_outputs.append(output)
        
        # 加权组合
        stacked_outputs = torch.stack(strategy_outputs, dim=1)  # [batch, num_strategies, hidden_dim]
        weighted_output = torch.sum(
            stacked_outputs * strategy_weights.unsqueeze(-1), 
            dim=1
        )  # [batch, hidden_dim]
        
        return weighted_output


# 融合模块工厂
class FusionFactory:
    """融合模块工厂 - 根据配置创建不同的融合策略"""
    
    @staticmethod
    def create_fusion_module(config: Dict[str, Any]) -> FusionModule:
        """
        根据配置创建融合模块
        
        Args:
            config: 融合配置，包含type字段指定融合类型
            
        Returns:
            融合模块实例
        """
        fusion_type = config.get('fusion_type', 'cross_attention')
        
        if fusion_type == 'cross_attention':
            return CrossAttentionFusion(config)
        elif fusion_type == 'gated':
            return GatedFusion(config)
        elif fusion_type == 'bilinear':
            return BilinearFusion(config)
        elif fusion_type == 'simple':
            return SimpleFusion(config)
        elif fusion_type == 'adaptive':
            return AdaptiveFusion(config)
        else:
            raise ValueError(f"不支持的融合类型: {fusion_type}")


# 注册融合模块
fusion_registry = {
    'cross_attention': CrossAttentionFusion,
    'gated': GatedFusion,
    'bilinear': BilinearFusion,
    'simple': SimpleFusion,
    'adaptive': AdaptiveFusion
}


def create_fusion_module(config: Dict[str, Any]) -> FusionModule:
    """创建融合模块的便利函数"""
    return FusionFactory.create_fusion_module(config)


if __name__ == "__main__":
    # 测试融合模块
    config = {
        'spatial_dim': 256,
        'temporal_dim': 256,
        'hidden_dim': 512,
        'num_heads': 8,
        'dropout': 0.1
    }
    
    # 测试数据
    batch_size = 4
    spatial_features = torch.randn(batch_size, 256)
    temporal_features = torch.randn(batch_size, 256)
    
    # 测试不同融合策略
    fusion_types = ['cross_attention', 'gated', 'bilinear', 'simple', 'adaptive']
    
    for fusion_type in fusion_types:
        if fusion_type == 'simple':
            test_config = {**config, 'fusion_type': 'concat'}
        else:
            test_config = {**config, 'fusion_type': fusion_type}
        
        fusion = create_fusion_module(test_config)
        output = fusion(spatial_features, temporal_features)
        
        param_count = sum(p.numel() for p in fusion.parameters())
        print(f"{fusion_type} 融合:")
        print(f"  输出形状: {output.shape}")
        print(f"  参数量: {param_count}")
        print()
    
    print("融合模块测试完成！")