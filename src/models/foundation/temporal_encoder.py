"""
时间编码器实现
==============

使用Transformer架构处理MCG信号的时序依赖：
- 位置编码支持变长序列
- 多头自注意力捕获长距离依赖
- 可配置的层数和维度
- 支持因果掩码和padding掩码
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, List, Any
import math

from ..interfaces.base_encoder import TemporalEncoder, EncoderRegistry


class PositionalEncoding(nn.Module):
    """位置编码 - 支持变长序列"""
    
    def __init__(self, d_model: int, max_seq_len: int = 5000, dropout: float = 0.1):
        super().__init__()
        self.dropout = nn.Dropout(dropout)
        self.d_model = d_model
        
        # 创建位置编码表
        pe = torch.zeros(max_seq_len, d_model)
        position = torch.arange(0, max_seq_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                            (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)  # [max_seq_len, 1, d_model]
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: [seq_len, batch, d_model]
        Returns:
            位置编码后的张量
        """
        seq_len = x.size(0)
        x = x + self.pe[:seq_len, :, :x.size(2)]
        return self.dropout(x)


class LearnablePositionalEncoding(nn.Module):
    """可学习的位置编码"""
    
    def __init__(self, d_model: int, max_seq_len: int = 5000, dropout: float = 0.1):
        super().__init__()
        self.dropout = nn.Dropout(dropout)
        self.pe = nn.Parameter(torch.randn(max_seq_len, 1, d_model))
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        seq_len = x.size(0)
        x = x + self.pe[:seq_len]
        return self.dropout(x)


class TransformerBlock(nn.Module):
    """Transformer编码器块"""
    
    def __init__(self, d_model: int, nhead: int, dim_feedforward: int,
                 dropout: float = 0.1, activation: str = 'relu'):
        super().__init__()
        
        # 多头自注意力
        self.self_attn = nn.MultiheadAttention(
            d_model, nhead, dropout=dropout, batch_first=False
        )
        
        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(d_model, dim_feedforward),
            nn.ReLU() if activation == 'relu' else nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(dim_feedforward, d_model)
        )
        
        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # Dropout
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
    
    def forward(self, x: torch.Tensor, 
                src_mask: Optional[torch.Tensor] = None,
                src_key_padding_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            x: [seq_len, batch, d_model]
            src_mask: 注意力掩码
            src_key_padding_mask: padding掩码 [batch, seq_len]
            
        Returns:
            (output, attention_weights)
        """
        # 自注意力
        attn_output, attn_weights = self.self_attn(
            x, x, x, 
            attn_mask=src_mask,
            key_padding_mask=src_key_padding_mask,
            average_attn_weights=True
        )
        
        # 残差连接和层归一化
        x = self.norm1(x + self.dropout1(attn_output))
        
        # 前馈网络
        ffn_output = self.ffn(x)
        x = self.norm2(x + self.dropout2(ffn_output))
        
        return x, attn_weights


class MCGTemporalEncoder(TemporalEncoder):
    """MCG时间编码器 - Transformer实现"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # 配置参数
        self.d_model = config.get('d_model', 256)
        self.nhead = config.get('nhead', 8)
        self.num_layers = config.get('num_layers', 4)
        self.dim_feedforward = config.get('dim_feedforward', 1024)
        self.dropout = config.get('dropout', 0.1)
        self.max_seq_len = config.get('max_seq_len', 1500)
        self.activation = config.get('activation', 'relu')
        self.positional_encoding = config.get('positional_encoding', 'sinusoidal')
        
        # 输入投影层
        # 将通道特征投影到d_model维度
        self.input_projection = nn.Linear(36, self.d_model)  # 36个MCG通道
        
        # 位置编码
        if self.positional_encoding == 'sinusoidal':
            self.pos_encoder = PositionalEncoding(
                self.d_model, self.max_seq_len, self.dropout
            )
        elif self.positional_encoding == 'learnable':
            self.pos_encoder = LearnablePositionalEncoding(
                self.d_model, self.max_seq_len, self.dropout
            )
        else:
            self.pos_encoder = nn.Identity()
        
        # Transformer层
        self.transformer_layers = nn.ModuleList([
            TransformerBlock(
                self.d_model, self.nhead, self.dim_feedforward,
                self.dropout, self.activation
            ) for _ in range(self.num_layers)
        ])
        
        # 输出投影
        self.output_projection = nn.Linear(self.d_model, self.d_model)
        
        # 用于全局表征的聚合
        self.global_pooling = config.get('global_pooling', 'mean')  # ['mean', 'max', 'cls', 'attention']
        
        if self.global_pooling == 'cls':
            # 添加CLS token
            self.cls_token = nn.Parameter(torch.randn(1, 1, self.d_model))
        elif self.global_pooling == 'attention':
            # 注意力池化
            self.attention_pool = nn.Sequential(
                nn.Linear(self.d_model, self.d_model // 4),
                nn.ReLU(),
                nn.Linear(self.d_model // 4, 1)
            )
        
        self.output_dim = self.d_model
    
    def get_output_dim(self) -> int:
        return self.output_dim
    
    def create_padding_mask(self, x: torch.Tensor, lengths: Optional[torch.Tensor] = None) -> Optional[torch.Tensor]:
        """
        创建padding掩码
        
        Args:
            x: [batch, seq_len, channels]
            lengths: 每个序列的实际长度 [batch]
            
        Returns:
            padding_mask: [batch, seq_len], True表示padding位置
        """
        if lengths is None:
            return None
        
        batch_size, seq_len = x.shape[0], x.shape[1]
        mask = torch.arange(seq_len).expand(batch_size, seq_len) >= lengths.unsqueeze(1)
        return mask.to(x.device)
    
    def forward(self, x: torch.Tensor, 
                mask: Optional[torch.Tensor] = None,
                lengths: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播
        
        Args:
            x: 输入信号 [batch, seq_len, 36]
            mask: 注意力掩码 [seq_len, seq_len] (可选)
            lengths: 序列长度 [batch] (用于padding mask)
            
        Returns:
            (temporal_features, attention_weights_dict)
        """
        batch_size, seq_len, channels = x.shape
        
        # 输入投影
        x = self.input_projection(x)  # [batch, seq_len, d_model]
        
        # 添加CLS token (如果使用)
        if self.global_pooling == 'cls':
            cls_tokens = self.cls_token.expand(-1, batch_size, -1)  # [1, batch, d_model]
            x = x.transpose(0, 1)  # [seq_len, batch, d_model]
            x = torch.cat([cls_tokens, x], dim=0)  # [seq_len+1, batch, d_model]
            seq_len += 1
        else:
            x = x.transpose(0, 1)  # [seq_len, batch, d_model]
        
        # 位置编码
        x = self.pos_encoder(x)
        
        # 创建padding掩码
        padding_mask = self.create_padding_mask(x.transpose(0, 1), lengths)
        if padding_mask is not None and self.global_pooling == 'cls':
            # CLS token不需要掩码
            cls_mask = torch.zeros(batch_size, 1, dtype=torch.bool, device=x.device)
            padding_mask = torch.cat([cls_mask, padding_mask], dim=1)
        
        # 通过Transformer层
        attention_weights = {}
        for i, layer in enumerate(self.transformer_layers):
            x, attn_weights = layer(x, src_mask=mask, src_key_padding_mask=padding_mask)
            attention_weights[f'layer_{i}'] = attn_weights
        
        # 输出投影
        x = self.output_projection(x)  # [seq_len, batch, d_model]
        
        # 全局表征聚合
        x = x.transpose(0, 1)  # [batch, seq_len, d_model]
        
        if self.global_pooling == 'cls':
            # 使用CLS token
            global_repr = x[:, 0, :]  # [batch, d_model]
            sequence_repr = x[:, 1:, :]  # [batch, seq_len, d_model]
        elif self.global_pooling == 'mean':
            # 平均池化
            if padding_mask is not None:
                # 排除padding位置
                mask_expanded = (~padding_mask).unsqueeze(-1).float()
                global_repr = (x * mask_expanded).sum(dim=1) / mask_expanded.sum(dim=1)
            else:
                global_repr = x.mean(dim=1)
            sequence_repr = x
        elif self.global_pooling == 'max':
            # 最大池化
            if padding_mask is not None:
                x_masked = x.masked_fill(padding_mask.unsqueeze(-1), float('-inf'))
                global_repr = x_masked.max(dim=1)[0]
            else:
                global_repr = x.max(dim=1)[0]
            sequence_repr = x
        elif self.global_pooling == 'attention':
            # 注意力池化
            attn_scores = self.attention_pool(x)  # [batch, seq_len, 1]
            if padding_mask is not None:
                attn_scores = attn_scores.masked_fill(padding_mask.unsqueeze(-1), float('-inf'))
            attn_weights = F.softmax(attn_scores, dim=1)
            global_repr = (x * attn_weights).sum(dim=1)  # [batch, d_model]
            sequence_repr = x
            attention_weights['global_pooling'] = attn_weights.squeeze(-1)
        else:
            global_repr = x.mean(dim=1)
            sequence_repr = x
        
        return global_repr, {
            'attention_weights': attention_weights,
            'sequence_features': sequence_repr,
            'global_features': global_repr
        }


class HierarchicalTemporalEncoder(TemporalEncoder):
    """分层时间编码器 - 多时间尺度建模"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # 配置参数
        self.scales = config.get('scales', [1, 4, 16])  # 不同的时间尺度
        self.d_model = config.get('d_model', 256)
        self.nhead = config.get('nhead', 8)
        self.num_layers = config.get('num_layers', 2)
        self.dropout = config.get('dropout', 0.1)
        
        # 为每个尺度创建编码器
        self.scale_encoders = nn.ModuleDict()
        for scale in self.scales:
            encoder_config = config.copy()
            encoder_config['num_layers'] = self.num_layers
            encoder_config['global_pooling'] = 'mean'
            
            self.scale_encoders[f'scale_{scale}'] = MCGTemporalEncoder(encoder_config)
        
        # 跨尺度融合
        self.cross_scale_fusion = nn.Sequential(
            nn.Linear(len(self.scales) * self.d_model, self.d_model),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.d_model, self.d_model)
        )
        
        self.output_dim = self.d_model
    
    def get_output_dim(self) -> int:
        return self.output_dim
    
    def downsample_sequence(self, x: torch.Tensor, scale: int) -> torch.Tensor:
        """对序列进行下采样"""
        if scale == 1:
            return x
        
        batch_size, seq_len, channels = x.shape
        
        # 池化下采样
        if seq_len % scale != 0:
            # Padding到scale的倍数
            pad_len = scale - (seq_len % scale)
            x = F.pad(x, (0, 0, 0, pad_len), mode='reflect')
            seq_len += pad_len
        
        # 重排并池化
        x = x.view(batch_size, seq_len // scale, scale, channels)
        x = x.mean(dim=2)  # 平均池化
        
        return x
    
    def forward(self, x: torch.Tensor, 
                mask: Optional[torch.Tensor] = None,
                lengths: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        分层时间编码
        
        Args:
            x: 输入信号 [batch, seq_len, 36]
            mask: 注意力掩码
            lengths: 序列长度
            
        Returns:
            (hierarchical_features, multi_scale_info)
        """
        scale_features = []
        all_attention_weights = {}
        
        # 每个时间尺度的编码
        for scale in self.scales:
            # 下采样
            x_scaled = self.downsample_sequence(x, scale)
            
            # 调整lengths
            lengths_scaled = lengths // scale if lengths is not None else None
            
            # 编码
            encoder = self.scale_encoders[f'scale_{scale}']
            features, info = encoder(x_scaled, mask, lengths_scaled)
            
            scale_features.append(features)
            all_attention_weights[f'scale_{scale}'] = info['attention_weights']
        
        # 跨尺度融合
        combined_features = torch.cat(scale_features, dim=1)
        fused_features = self.cross_scale_fusion(combined_features)
        
        return fused_features, {
            'attention_weights': all_attention_weights,
            'scale_features': scale_features,
            'fused_features': fused_features
        }


# 注册编码器
EncoderRegistry.register('mcg_temporal', MCGTemporalEncoder)
EncoderRegistry.register('hierarchical_temporal', HierarchicalTemporalEncoder)


def create_temporal_encoder(config: Dict[str, Any]) -> TemporalEncoder:
    """创建时间编码器的便利函数"""
    encoder_type = config.get('type', 'mcg_temporal')
    
    if encoder_type == 'mcg_temporal':
        return MCGTemporalEncoder(config)
    elif encoder_type == 'hierarchical_temporal':
        return HierarchicalTemporalEncoder(config)
    else:
        raise ValueError(f"不支持的时间编码器类型: {encoder_type}")


if __name__ == "__main__":
    # 测试时间编码器
    config = {
        'd_model': 256,
        'nhead': 8,
        'num_layers': 4,
        'dim_feedforward': 1024,
        'dropout': 0.1,
        'max_seq_len': 1500,
        'positional_encoding': 'sinusoidal',
        'global_pooling': 'attention'
    }
    
    encoder = MCGTemporalEncoder(config)
    print(f"时间编码器参数量: {sum(p.numel() for p in encoder.parameters())}")
    
    # 测试数据
    batch_size, seq_len, channels = 4, 1500, 36
    x = torch.randn(batch_size, seq_len, channels)
    
    # 模拟变长序列
    lengths = torch.tensor([1500, 1200, 800, 1000])
    
    # 前向传播
    features, info = encoder(x, lengths=lengths)
    
    print(f"输入形状: {x.shape}")
    print(f"输出特征形状: {features.shape}")
    print(f"注意力权重层数: {len(info['attention_weights'])}")
    print(f"序列特征形状: {info['sequence_features'].shape}")
    
    # 测试分层编码器
    hierarchical_config = {
        'scales': [1, 4, 16],
        'd_model': 256,
        'nhead': 8,
        'num_layers': 2,
        'dropout': 0.1
    }
    
    hierarchical_encoder = HierarchicalTemporalEncoder(hierarchical_config)
    h_features, h_info = hierarchical_encoder(x, lengths=lengths)
    
    print(f"分层编码器输出形状: {h_features.shape}")
    print(f"尺度特征数量: {len(h_info['scale_features'])}")
    
    print("时间编码器测试完成！")