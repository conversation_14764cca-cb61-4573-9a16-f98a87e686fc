"""
空间编码器实现
==============

处理MCG信号的36通道空间关系：
- 36通道重排为6x6空间网格
- 使用2D CNN提取空间特征
- 支持多尺度特征提取
- 可配置的池化和归一化策略
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, List, Any
import math

from ..interfaces.base_encoder import SpatialEncoder, EncoderRegistry


class Conv2DBlock(nn.Module):
    """2D卷积块 - 基础构建单元"""
    
    def __init__(self, in_channels: int, out_channels: int,
                 kernel_size: int = 3, stride: int = 1, padding: int = 1,
                 use_bn: bool = True, activation: str = 'relu',
                 dropout: float = 0.0):
        super().__init__()
        
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, 
                             stride, padding, bias=not use_bn)
        
        self.bn = nn.BatchNorm2d(out_channels) if use_bn else nn.Identity()
        
        # 激活函数
        if activation == 'relu':
            self.activation = nn.ReLU(inplace=True)
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'swish':
            self.activation = nn.SiLU()
        else:
            self.activation = nn.Identity()
        
        self.dropout = nn.Dropout2d(dropout) if dropout > 0 else nn.Identity()
    
    def forward(self, x):
        x = self.conv(x)
        x = self.bn(x)
        x = self.activation(x)
        x = self.dropout(x)
        return x


class SpatialAttention(nn.Module):
    """空间注意力模块"""
    
    def __init__(self, channels: int, reduction: int = 16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )
        
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        # Channel attention
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        channel_att = self.sigmoid(avg_out + max_out)
        
        # Spatial attention
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        spatial_att = torch.cat([avg_out, max_out], dim=1)
        spatial_att = nn.Conv2d(2, 1, 7, padding=3, bias=False).to(x.device)(spatial_att)
        spatial_att = torch.sigmoid(spatial_att)
        
        return x * channel_att * spatial_att


class MCGSpatialEncoder(SpatialEncoder):
    """MCG空间编码器 - CNN实现"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # 配置参数
        self.input_channels = config.get('input_channels', 36)
        self.conv_channels = config.get('conv_channels', [64, 128, 256])
        self.kernel_sizes = config.get('kernel_sizes', [3, 3, 3])
        self.pooling = config.get('pooling', 'adaptive')
        self.dropout = config.get('dropout', 0.1)
        self.use_attention = config.get('use_attention', True)
        self.activation = config.get('activation', 'relu')
        
        # 确保输入是36通道
        assert self.input_channels == 36, "MCG输入必须是36通道"
        
        # 构建网络层
        self._build_network()
        
        # 计算输出维度
        self.output_dim = self._calculate_output_dim()
    
    def _build_network(self):
        """构建网络结构"""
        layers = []
        in_channels = 1  # 时间序列在第一维，每个时间点有36个通道值重排为6x6
        
        # 主干CNN层
        for i, out_channels in enumerate(self.conv_channels):
            kernel_size = self.kernel_sizes[i] if i < len(self.kernel_sizes) else 3
            
            layers.append(Conv2DBlock(
                in_channels, out_channels, kernel_size,
                stride=1, padding=kernel_size//2,
                use_bn=True, activation=self.activation,
                dropout=self.dropout if i > 0 else 0
            ))
            
            # 添加池化层 (除了最后一层)
            if i < len(self.conv_channels) - 1:
                if self.pooling == 'max':
                    layers.append(nn.MaxPool2d(2, 2))
                elif self.pooling == 'avg':
                    layers.append(nn.AvgPool2d(2, 2))
                # adaptive pooling在最后处理
            
            in_channels = out_channels
        
        self.conv_layers = nn.ModuleList(layers)
        
        # 空间注意力
        if self.use_attention:
            self.spatial_attention = SpatialAttention(self.conv_channels[-1])
        
        # 自适应池化到固定尺寸
        if self.pooling == 'adaptive':
            self.adaptive_pool = nn.AdaptiveAvgPool2d((2, 2))  # 池化到2x2
        
        # 最终特征投影
        pooled_size = 2 * 2 * self.conv_channels[-1]  # 2x2 grid * channels
        self.feature_proj = nn.Sequential(
            nn.Linear(pooled_size, 512),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(512, 256)
        )
    
    def _calculate_output_dim(self) -> int:
        """计算输出维度"""
        return 256  # 固定输出维度
    
    def get_output_dim(self) -> int:
        return self.output_dim
    
    def reshape_to_spatial(self, x: torch.Tensor) -> torch.Tensor:
        """
        将36通道信号重排为6x6空间网格
        
        Args:
            x: [batch, seq_len, 36] 或 [batch, 36, seq_len]
            
        Returns:
            [batch, seq_len, 6, 6] 或 [batch, 1, 6, 6]
        """
        if x.dim() == 3 and x.shape[-1] == 36:
            # [batch, seq_len, 36] -> [batch, seq_len, 6, 6]
            batch_size, seq_len, channels = x.shape
            return x.view(batch_size, seq_len, 6, 6)
        
        elif x.dim() == 3 and x.shape[1] == 36:
            # [batch, 36, seq_len] -> [batch, seq_len, 6, 6]
            batch_size, channels, seq_len = x.shape
            x = x.transpose(1, 2)  # [batch, seq_len, 36]
            return x.view(batch_size, seq_len, 6, 6)
        
        elif x.dim() == 2 and x.shape[-1] == 36:
            # [batch, 36] -> [batch, 1, 6, 6]
            batch_size, channels = x.shape
            return x.view(batch_size, 1, 6, 6)
        
        else:
            raise ValueError(f"不支持的输入形状: {x.shape}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入信号，多种格式支持：
               - [batch, seq_len, 36]: 时序信号
               - [batch, 36, seq_len]: 转置时序信号  
               - [batch, 36]: 单时间点信号
               
        Returns:
            空间特征 [batch, output_dim]
        """
        batch_size = x.shape[0]
        
        # 重排为6x6空间网格
        spatial_data = self.reshape_to_spatial(x)  # [batch, seq_len, 6, 6]
        
        # 如果是时序数据，需要沿时间维度池化或选择代表性时间点
        if spatial_data.shape[1] > 1:  # 有时间维度
            # 方法1: 平均池化
            spatial_data = spatial_data.mean(dim=1, keepdim=True)  # [batch, 1, 6, 6]
            
            # 方法2: 选择中间时间点 (可选)
            # mid_t = spatial_data.shape[1] // 2
            # spatial_data = spatial_data[:, mid_t:mid_t+1, :, :]
        
        # 调整为CNN输入格式: [batch, channels, height, width]
        spatial_data = spatial_data.transpose(1, 3).transpose(2, 3)  # [batch, 6, 6, 1] -> [batch, 1, 6, 6]
        
        # 通过CNN层
        features = spatial_data
        intermediate_features = {}
        
        for i, layer in enumerate(self.conv_layers):
            features = layer(features)
            if isinstance(layer, Conv2DBlock):
                intermediate_features[f'conv_{i}'] = features
        
        # 空间注意力
        if self.use_attention:
            features = self.spatial_attention(features)
        
        # 自适应池化
        if hasattr(self, 'adaptive_pool'):
            features = self.adaptive_pool(features)  # [batch, channels, 2, 2]
        
        # 展平并投影
        features = features.view(batch_size, -1)  # [batch, channels * 2 * 2]
        features = self.feature_proj(features)    # [batch, output_dim]
        
        return features


class MultiScaleSpatialEncoder(SpatialEncoder):
    """多尺度空间编码器 - 捕获不同尺度的空间特征"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # 多个尺度的卷积分支
        self.scales = config.get('scales', [1, 2, 3])  # 不同kernel size
        self.base_channels = config.get('base_channels', 64)
        self.dropout = config.get('dropout', 0.1)
        
        # 为每个尺度创建卷积分支
        self.scale_branches = nn.ModuleList()
        for scale in self.scales:
            kernel_size = 2 * scale + 1  # 3, 5, 7
            branch = nn.Sequential(
                nn.Conv2d(1, self.base_channels, kernel_size, 
                         padding=kernel_size//2),
                nn.BatchNorm2d(self.base_channels),
                nn.ReLU(),
                nn.Conv2d(self.base_channels, self.base_channels, 3, padding=1),
                nn.BatchNorm2d(self.base_channels),
                nn.ReLU(),
                nn.AdaptiveAvgPool2d((2, 2))
            )
            self.scale_branches.append(branch)
        
        # 特征融合
        total_features = len(self.scales) * self.base_channels * 4  # 4 = 2*2
        self.fusion = nn.Sequential(
            nn.Linear(total_features, 512),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(512, 256)
        )
        
        self.output_dim = 256
    
    def get_output_dim(self) -> int:
        return self.output_dim
    
    def reshape_to_spatial(self, x: torch.Tensor) -> torch.Tensor:
        """同MCGSpatialEncoder的实现"""
        if x.dim() == 3 and x.shape[-1] == 36:
            batch_size, seq_len, channels = x.shape
            return x.view(batch_size, seq_len, 6, 6)
        elif x.dim() == 3 and x.shape[1] == 36:
            batch_size, channels, seq_len = x.shape
            x = x.transpose(1, 2)
            return x.view(batch_size, seq_len, 6, 6)
        elif x.dim() == 2 and x.shape[-1] == 36:
            batch_size, channels = x.shape
            return x.view(batch_size, 1, 6, 6)
        else:
            raise ValueError(f"不支持的输入形状: {x.shape}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        多尺度空间特征提取
        
        Args:
            x: 输入信号 [batch, seq_len, 36]
            
        Returns:
            多尺度空间特征 [batch, output_dim]
        """
        batch_size = x.shape[0]
        
        # 重排为空间网格
        spatial_data = self.reshape_to_spatial(x)
        
        # 时间维度处理
        if spatial_data.shape[1] > 1:
            spatial_data = spatial_data.mean(dim=1, keepdim=True)
        
        # 调整为CNN格式
        spatial_data = spatial_data.transpose(1, 3).transpose(2, 3)  # [batch, 1, 6, 6]
        
        # 多尺度特征提取
        scale_features = []
        for branch in self.scale_branches:
            features = branch(spatial_data)  # [batch, base_channels, 2, 2]
            features = features.view(batch_size, -1)  # [batch, base_channels * 4]
            scale_features.append(features)
        
        # 特征融合
        combined = torch.cat(scale_features, dim=1)  # [batch, total_features]
        output = self.fusion(combined)
        
        return output


# 注册编码器
EncoderRegistry.register('mcg_spatial', MCGSpatialEncoder)
EncoderRegistry.register('multiscale_spatial', MultiScaleSpatialEncoder)


def create_spatial_encoder(config: Dict[str, Any]) -> SpatialEncoder:
    """创建空间编码器的便利函数"""
    encoder_type = config.get('type', 'mcg_spatial')
    
    if encoder_type == 'mcg_spatial':
        return MCGSpatialEncoder(config)
    elif encoder_type == 'multiscale_spatial':
        return MultiScaleSpatialEncoder(config)
    else:
        raise ValueError(f"不支持的空间编码器类型: {encoder_type}")


if __name__ == "__main__":
    # 测试空间编码器
    config = {
        'input_channels': 36,
        'conv_channels': [64, 128, 256],
        'kernel_sizes': [3, 3, 3],
        'pooling': 'adaptive',
        'dropout': 0.1,
        'use_attention': True,
        'activation': 'relu'
    }
    
    encoder = MCGSpatialEncoder(config)
    print(f"空间编码器参数量: {sum(p.numel() for p in encoder.parameters())}")
    
    # 测试不同输入格式
    batch_size, seq_len, channels = 4, 1500, 36
    
    # 格式1: [batch, seq_len, 36]
    x1 = torch.randn(batch_size, seq_len, channels)
    out1 = encoder(x1)
    print(f"输入形状 {x1.shape} -> 输出形状 {out1.shape}")
    
    # 格式2: [batch, 36]
    x2 = torch.randn(batch_size, channels)
    out2 = encoder(x2)
    print(f"输入形状 {x2.shape} -> 输出形状 {out2.shape}")
    
    # 测试多尺度编码器
    multiscale_config = {
        'scales': [1, 2, 3],
        'base_channels': 64,
        'dropout': 0.1
    }
    
    multiscale_encoder = MultiScaleSpatialEncoder(multiscale_config)
    out3 = multiscale_encoder(x1)
    print(f"多尺度编码器输出形状: {out3.shape}")
    
    print("空间编码器测试完成！")