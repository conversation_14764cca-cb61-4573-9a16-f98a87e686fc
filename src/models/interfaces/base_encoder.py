"""
基础编码器接口定义
==================

为MCG三阶段项目定义统一的编码器接口标准：
- 第一阶段：基础编码器在MAE预训练中学习通用表征
- 第二阶段：基础编码器作为专家模型的特征提取器
- 第三阶段：基础编码器用于科学发现和模型解释

核心设计原则：
1. 统一输入输出格式
2. 可冻结/可微调的灵活性
3. 中间特征提取能力
4. 配置驱动的架构适配
"""

import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import Dict, Tuple, Optional, List, Any
from dataclasses import dataclass


@dataclass
class EncoderOutput:
    """编码器输出标准格式"""
    # 主要特征表征
    features: torch.Tensor                    # [batch, feature_dim]
    
    # 中间层特征 (用于分析和调试)
    intermediate_features: Dict[str, torch.Tensor]  # {layer_name: [batch, ...]}
    
    # 注意力权重 (用于解释性分析)
    attention_weights: Optional[Dict[str, torch.Tensor]] = None
    
    # 空间特征 (6x6网格特征)
    spatial_features: Optional[torch.Tensor] = None    # [batch, 6, 6, spatial_dim]
    
    # 时间特征 (序列特征)
    temporal_features: Optional[torch.Tensor] = None   # [batch, seq_len, temporal_dim]
    
    # 元信息
    metadata: Optional[Dict[str, Any]] = None


class BaseEncoder(nn.Module, ABC):
    """基础编码器抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.is_frozen = False
        
        # 注册中间特征提取钩子
        self._feature_hooks = {}
        self._register_feature_hooks()
    
    @abstractmethod
    def forward(self, x: torch.Tensor, 
                return_intermediate: bool = False,
                return_attention: bool = False) -> EncoderOutput:
        """
        前向传播
        
        Args:
            x: 输入信号 [batch, seq_len, channels]
            return_intermediate: 是否返回中间特征
            return_attention: 是否返回注意力权重
            
        Returns:
            EncoderOutput 对象
        """
        pass
    
    @abstractmethod
    def get_output_dim(self) -> int:
        """获取输出特征维度"""
        pass
    
    @abstractmethod
    def get_spatial_output_dim(self) -> Tuple[int, int, int]:
        """获取空间特征维度 (height, width, channels)"""
        pass
    
    @abstractmethod
    def get_temporal_output_dim(self) -> Tuple[int, int]:
        """获取时间特征维度 (seq_len, channels)"""
        pass
    
    def freeze_encoder(self):
        """冻结编码器参数"""
        for param in self.parameters():
            param.requires_grad = False
        self.is_frozen = True
    
    def unfreeze_encoder(self):
        """解冻编码器参数"""
        for param in self.parameters():
            param.requires_grad = True
        self.is_frozen = False
    
    def freeze_layers(self, layer_names: List[str]):
        """冻结指定层"""
        for name, module in self.named_modules():
            if any(layer_name in name for layer_name in layer_names):
                for param in module.parameters():
                    param.requires_grad = False
    
    def _register_feature_hooks(self):
        """注册特征提取钩子"""
        def create_hook(name):
            def hook(module, input, output):
                if isinstance(output, torch.Tensor):
                    self._feature_hooks[name] = output.detach().clone()
                elif isinstance(output, tuple):
                    self._feature_hooks[name] = output[0].detach().clone()
            return hook
        
        # 子类需要重写此方法来注册具体的钩子
        pass
    
    def get_intermediate_features(self) -> Dict[str, torch.Tensor]:
        """获取中间层特征"""
        return self._feature_hooks.copy()
    
    def clear_feature_hooks(self):
        """清除特征钩子"""
        self._feature_hooks.clear()
    
    def load_pretrained(self, checkpoint_path: str, strict: bool = True):
        """加载预训练权重"""
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        # 提取encoder权重
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        elif 'encoder' in checkpoint:
            state_dict = checkpoint['encoder']
        else:
            state_dict = checkpoint
        
        # 过滤encoder相关权重
        encoder_state_dict = {}
        for key, value in state_dict.items():
            if 'encoder' in key or 'foundation' in key:
                # 移除前缀
                new_key = key.replace('encoder.', '').replace('foundation_encoder.', '')
                encoder_state_dict[new_key] = value
        
        self.load_state_dict(encoder_state_dict, strict=strict)
    
    def save_encoder(self, save_path: str, additional_info: Optional[Dict] = None):
        """保存编码器权重"""
        save_dict = {
            'encoder_state_dict': self.state_dict(),
            'config': self.config,
            'output_dim': self.get_output_dim(),
            'is_frozen': self.is_frozen
        }
        
        if additional_info:
            save_dict.update(additional_info)
        
        torch.save(save_dict, save_path)


class SpatialEncoder(nn.Module, ABC):
    """空间编码器抽象基类 - 处理36通道的6x6空间关系"""
    
    @abstractmethod
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        空间编码
        
        Args:
            x: 输入信号 [batch, seq_len, 36] 或 [batch, 36, seq_len]
            
        Returns:
            空间特征 [batch, spatial_dim] 或 [batch, 6, 6, spatial_dim]
        """
        pass


class TemporalEncoder(nn.Module, ABC):
    """时间编码器抽象基类 - 处理时序依赖"""
    
    @abstractmethod
    def forward(self, x: torch.Tensor, 
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Optional[Dict]]:
        """
        时间编码
        
        Args:
            x: 输入信号 [batch, seq_len, channels]
            mask: 注意力掩码 [batch, seq_len]
            
        Returns:
            (temporal_features, attention_weights)
        """
        pass


class FusionModule(nn.Module, ABC):
    """时空融合模块抽象基类"""
    
    @abstractmethod
    def forward(self, spatial_features: torch.Tensor, 
                temporal_features: torch.Tensor) -> torch.Tensor:
        """
        时空特征融合
        
        Args:
            spatial_features: 空间特征
            temporal_features: 时间特征
            
        Returns:
            融合特征
        """
        pass


class EncoderRegistry:
    """编码器注册器 - 便于不同阶段使用不同的编码器实现"""
    
    _encoders = {}
    
    @classmethod
    def register(cls, name: str, encoder_class: type):
        """注册编码器"""
        cls._encoders[name] = encoder_class
    
    @classmethod
    def get_encoder(cls, name: str) -> type:
        """获取编码器类"""
        if name not in cls._encoders:
            raise ValueError(f"未注册的编码器: {name}")
        return cls._encoders[name]
    
    @classmethod
    def list_encoders(cls) -> List[str]:
        """列出所有注册的编码器"""
        return list(cls._encoders.keys())
    
    @classmethod
    def create_encoder(cls, name: str, config: Dict[str, Any]) -> BaseEncoder:
        """创建编码器实例"""
        encoder_class = cls.get_encoder(name)
        return encoder_class(config)


# 便利函数
def create_encoder_from_config(config: Dict[str, Any]) -> BaseEncoder:
    """从配置创建编码器"""
    encoder_type = config.get('encoder_type', 'hybrid_spatiotemporal')
    
    if encoder_type not in EncoderRegistry.list_encoders():
        raise ValueError(f"不支持的编码器类型: {encoder_type}")
    
    return EncoderRegistry.create_encoder(encoder_type, config)


def load_pretrained_encoder(checkpoint_path: str, 
                           config: Optional[Dict[str, Any]] = None) -> BaseEncoder:
    """加载预训练编码器"""
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # 获取配置
    if config is None:
        config = checkpoint.get('config', {})
    
    # 创建编码器
    encoder = create_encoder_from_config(config)
    
    # 加载权重
    encoder.load_state_dict(checkpoint['encoder_state_dict'])
    
    return encoder