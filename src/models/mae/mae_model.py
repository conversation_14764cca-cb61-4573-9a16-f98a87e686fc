"""
MAE模型实现
===========

整合所有组件的完整MAE模型：
- 混合时空编码器 (空间CNN + 时间Transformer + 交叉注意力融合)
- 可配置的掩码策略
- 轻量级解码器
- 重建损失计算
- 标准化的输入输出接口
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, List, Any
import math

from ..interfaces.base_encoder import BaseEncoder, EncoderOutput, EncoderRegistry
from ..foundation.spatial_encoder import create_spatial_encoder
from ..foundation.temporal_encoder import create_temporal_encoder  
from ..foundation.fusion_module import create_fusion_module
from .masking_strategy import create_masking_strategy

import logging
logger = logging.getLogger(__name__)


class MCGMAEEncoder(BaseEncoder):
    """MCG MAE编码器 - 混合时空架构"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 基础配置
        self.input_channels = config.get('input_channels', 36)
        self.hidden_dim = config.get('hidden_dim', 512)
        self.dropout = config.get('dropout', 0.1)
        
        # 空间编码器配置
        spatial_config = config.get('spatial_encoder', {
            'type': 'mcg_spatial',
            'input_channels': self.input_channels,
            'conv_channels': [64, 128, 256],
            'dropout': self.dropout
        })
        
        # 时间编码器配置
        temporal_config = config.get('temporal_encoder', {
            'type': 'mcg_temporal', 
            'd_model': 256,
            'nhead': 8,
            'num_layers': 4,
            'dropout': self.dropout,
            'global_pooling': 'attention'
        })
        
        # 融合模块配置
        fusion_config = config.get('fusion_module', {
            'fusion_type': 'cross_attention',
            'spatial_dim': 256,
            'temporal_dim': 256,
            'hidden_dim': self.hidden_dim,
            'dropout': self.dropout
        })
        
        # 创建组件
        self.spatial_encoder = create_spatial_encoder(spatial_config)
        self.temporal_encoder = create_temporal_encoder(temporal_config)
        self.fusion_module = create_fusion_module(fusion_config)
        
        # 输出投影
        self.output_projection = nn.Sequential(
            nn.LayerNorm(self.hidden_dim),
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout)
        )
        
        # 注册特征提取钩子
        self._register_feature_hooks()
    
    def _register_feature_hooks(self):
        """注册中间特征提取钩子"""
        def create_hook(name):
            def hook(module, input, output):
                if isinstance(output, torch.Tensor):
                    self._feature_hooks[name] = output.detach().clone()
                elif isinstance(output, tuple):
                    self._feature_hooks[name] = output[0].detach().clone()
            return hook
        
        # 为关键模块注册钩子
        self.spatial_encoder.register_forward_hook(create_hook('spatial_features'))
        self.temporal_encoder.register_forward_hook(create_hook('temporal_features'))
        self.fusion_module.register_forward_hook(create_hook('fused_features'))
    
    def get_output_dim(self) -> int:
        return self.hidden_dim
    
    def get_spatial_output_dim(self) -> Tuple[int, int, int]:
        return (6, 6, 256)  # 固定的空间特征维度
    
    def get_temporal_output_dim(self) -> Tuple[int, int]:
        return (1500, 256)  # 时间特征维度
    
    def forward(self, x: torch.Tensor, 
                return_intermediate: bool = False,
                return_attention: bool = False) -> EncoderOutput:
        """
        前向传播
        
        Args:
            x: 输入信号 [batch, seq_len, 36]
            return_intermediate: 是否返回中间特征
            return_attention: 是否返回注意力权重
            
        Returns:
            EncoderOutput 包含所有特征和元信息
        """
        batch_size, seq_len, channels = x.shape
        
        # 清除之前的特征钩子
        self.clear_feature_hooks()
        
        # 空间特征提取
        spatial_features = self.spatial_encoder(x)  # [batch, spatial_dim]
        
        # 时间特征提取
        temporal_features, temporal_info = self.temporal_encoder(x)  # [batch, temporal_dim]
        
        # 时空融合
        fused_features = self.fusion_module(spatial_features, temporal_features)
        
        # 输出投影
        output_features = self.output_projection(fused_features)
        
        # 构建输出
        encoder_output = EncoderOutput(
            features=output_features,
            intermediate_features=self.get_intermediate_features() if return_intermediate else {},
            attention_weights=temporal_info.get('attention_weights') if return_attention else None,
            spatial_features=spatial_features,
            temporal_features=temporal_features,
            metadata={
                'sequence_features': temporal_info.get('sequence_features'),
                'fusion_type': self.config.get('fusion_module', {}).get('fusion_type', 'cross_attention')
            }
        )
        
        return encoder_output


class MCGMAEDecoder(nn.Module):
    """MAE解码器 - 轻量级重建网络"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # 配置参数
        self.encoder_dim = config.get('encoder_dim', 512)
        self.decoder_dim = config.get('decoder_dim', 256)
        self.num_heads = config.get('num_heads', 8)
        self.num_layers = config.get('num_layers', 2)
        self.dropout = config.get('dropout', 0.1)
        
        # 输出维度
        self.output_channels = config.get('output_channels', 36)
        self.output_seq_len = config.get('output_seq_len', 1500)
        
        # 编码器特征投影
        self.encoder_to_decoder = nn.Linear(self.encoder_dim, self.decoder_dim)
        
        # 掩码token (可学习的参数)
        self.mask_token = nn.Parameter(torch.zeros(1, 1, self.decoder_dim))
        
        # 位置编码 (为解码器序列)
        self.decoder_pos_embed = nn.Parameter(
            torch.zeros(1, self.output_seq_len * self.output_channels, self.decoder_dim)
        )
        
        # Transformer解码器层
        decoder_layer = nn.TransformerDecoderLayer(
            d_model=self.decoder_dim,
            nhead=self.num_heads,
            dim_feedforward=self.decoder_dim * 4,
            dropout=self.dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_decoder = nn.TransformerDecoder(decoder_layer, self.num_layers)
        
        # 输出投影
        self.output_projection = nn.Linear(self.decoder_dim, 1)  # 每个token重建一个值
        
        # 初始化参数
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        torch.nn.init.normal_(self.mask_token, std=0.02)
        torch.nn.init.normal_(self.decoder_pos_embed, std=0.02)
    
    def forward(self, encoder_features: torch.Tensor, 
                mask: torch.Tensor) -> torch.Tensor:
        """
        解码器前向传播
        
        Args:
            encoder_features: 编码器特征 [batch, encoder_dim]
            mask: 掩码 [batch, seq_len, channels], 1=保留, 0=掩码
            
        Returns:
            重建信号 [batch, seq_len, channels]
        """
        batch_size, seq_len, channels = mask.shape
        total_tokens = seq_len * channels
        
        # 将编码器特征投影到解码器维度
        decoder_features = self.encoder_to_decoder(encoder_features)  # [batch, decoder_dim]
        
        # 创建解码器输入序列
        # 对于可见token，使用编码器特征；对于掩码token，使用mask_token
        mask_flat = mask.view(batch_size, total_tokens)  # [batch, seq_len * channels]
        
        # 广播编码器特征到所有token位置
        encoder_tokens = decoder_features.unsqueeze(1).expand(-1, total_tokens, -1)  # [batch, total_tokens, decoder_dim]
        
        # 掩码token
        mask_tokens = self.mask_token.expand(batch_size, total_tokens, -1)  # [batch, total_tokens, decoder_dim]
        
        # 根据mask选择token：1=编码器特征，0=mask_token
        decoder_input = torch.where(
            mask_flat.unsqueeze(-1) == 1,
            encoder_tokens,
            mask_tokens
        )  # [batch, total_tokens, decoder_dim]
        
        # 添加位置编码
        decoder_input = decoder_input + self.decoder_pos_embed
        
        # 创建memory (编码器输出作为memory)
        memory = encoder_features.unsqueeze(1)  # [batch, 1, encoder_dim]
        memory = self.encoder_to_decoder(memory)  # [batch, 1, decoder_dim]
        
        # Transformer解码
        decoded = self.transformer_decoder(decoder_input, memory)  # [batch, total_tokens, decoder_dim]
        
        # 输出投影
        reconstructed = self.output_projection(decoded)  # [batch, total_tokens, 1]
        reconstructed = reconstructed.squeeze(-1)  # [batch, total_tokens]
        
        # 重塑为原始形状
        reconstructed = reconstructed.view(batch_size, seq_len, channels)
        
        return reconstructed


class MCGMAE(nn.Module):
    """完整的MCG MAE模型"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # 创建编码器
        encoder_config = config.get('encoder', {})
        self.encoder = MCGMAEEncoder(encoder_config)
        
        # 创建解码器
        decoder_config = config.get('decoder', {
            'encoder_dim': self.encoder.get_output_dim(),
            'decoder_dim': config.get('decoder_dim', 256),
            'num_heads': config.get('decoder_num_heads', 8),
            'num_layers': config.get('decoder_num_layers', 2),
            'output_channels': 36,
            'output_seq_len': 1500
        })
        self.decoder = MCGMAEDecoder(decoder_config)
        
        # 创建掩码策略
        masking_config = config.get('masking', {
            'type': 'temporal_block',
            'mask_ratio': 0.2
        })
        self.masking_strategy = create_masking_strategy(masking_config)
        
        # 损失配置
        self.loss_type = config.get('loss_type', 'mse')
        self.normalize_target = config.get('normalize_target', True)
        self.mask_loss_only = config.get('mask_loss_only', True)  # 只计算掩码区域的损失
    
    def forward(self, x: torch.Tensor, 
                mask: Optional[torch.Tensor] = None,
                return_encoder_output: bool = False) -> Dict[str, torch.Tensor]:
        """
        MAE前向传播
        
        Args:
            x: 输入信号 [batch, seq_len, 36]
            mask: 预定义掩码 (可选)
            return_encoder_output: 是否返回编码器详细输出
            
        Returns:
            包含重建结果和损失的字典
        """
        batch_size, seq_len, channels = x.shape
        
        # 生成掩码 (如果未提供)
        if mask is None:
            if hasattr(self.masking_strategy, 'generate_mask'):
                # 检查是否需要原始信号 (自适应掩码)
                if 'adaptive' in self.config.get('masking', {}).get('type', ''):
                    mask = self.masking_strategy.generate_mask(batch_size, seq_len, channels, x)
                else:
                    mask = self.masking_strategy.generate_mask(batch_size, seq_len, channels)
            else:
                # 默认随机掩码
                mask = torch.rand(batch_size, seq_len, channels) > self.config.get('masking', {}).get('mask_ratio', 0.2)
                mask = mask.float()
        
        # 应用掩码到输入
        masked_input = x * mask
        
        # 编码
        encoder_output = self.encoder(
            masked_input, 
            return_intermediate=True, 
            return_attention=True
        )
        
        # 解码重建
        reconstructed = self.decoder(encoder_output.features, mask)
        
        # 计算损失
        loss_dict = self.compute_loss(x, reconstructed, mask)
        
        # 构建返回结果
        result = {
            'reconstructed': reconstructed,
            'mask': mask,
            'masked_input': masked_input,
            **loss_dict
        }
        
        if return_encoder_output:
            result['encoder_output'] = encoder_output
        
        return result
    
    def compute_loss(self, target: torch.Tensor, 
                    reconstructed: torch.Tensor,
                    mask: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算重建损失
        
        Args:
            target: 目标信号 [batch, seq_len, channels]
            reconstructed: 重建信号 [batch, seq_len, channels]
            mask: 掩码 [batch, seq_len, channels]
            
        Returns:
            损失字典
        """
        # 目标归一化 (可选)
        if self.normalize_target:
            target_norm = F.normalize(target, dim=1)  # 时间维度归一化
            reconstructed_norm = F.normalize(reconstructed, dim=1)
        else:
            target_norm = target
            reconstructed_norm = reconstructed
        
        if self.mask_loss_only:
            # 只计算掩码区域的损失
            masked_region = (mask == 0)
            if masked_region.sum() > 0:
                target_masked = target_norm[masked_region]
                recon_masked = reconstructed_norm[masked_region]
            else:
                # 如果没有掩码区域，使用全部区域
                target_masked = target_norm
                recon_masked = reconstructed_norm
        else:
            # 计算全部区域的损失
            target_masked = target_norm
            recon_masked = reconstructed_norm
        
        # 计算不同类型的损失
        loss_dict = {}
        
        if self.loss_type == 'mse':
            loss_dict['reconstruction_loss'] = F.mse_loss(recon_masked, target_masked)
        elif self.loss_type == 'mae' or self.loss_type == 'l1':
            loss_dict['reconstruction_loss'] = F.l1_loss(recon_masked, target_masked)
        elif self.loss_type == 'huber':
            loss_dict['reconstruction_loss'] = F.huber_loss(recon_masked, target_masked)
        elif self.loss_type == 'combined':
            mse_loss = F.mse_loss(recon_masked, target_masked)
            l1_loss = F.l1_loss(recon_masked, target_masked)
            loss_dict['reconstruction_loss'] = 0.7 * mse_loss + 0.3 * l1_loss
            loss_dict['mse_loss'] = mse_loss
            loss_dict['l1_loss'] = l1_loss
        
        # 额外的正则化损失 (可选)
        if self.config.get('use_feature_regularization', False):
            # 特征多样性正则化
            encoder_features = self.encoder(target * mask).features  # [batch, feature_dim]
            feature_std = torch.std(encoder_features, dim=0).mean()
            loss_dict['feature_diversity_loss'] = -torch.log(feature_std + 1e-8)  # 鼓励多样性
            
            # 总损失
            loss_dict['total_loss'] = (
                loss_dict['reconstruction_loss'] + 
                0.01 * loss_dict['feature_diversity_loss']
            )
        else:
            loss_dict['total_loss'] = loss_dict['reconstruction_loss']
        
        return loss_dict
    
    def encode(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> EncoderOutput:
        """只进行编码 (用于特征提取)"""
        if mask is not None:
            x = x * mask
        return self.encoder(x, return_intermediate=True, return_attention=True)
    
    def set_masking_epoch(self, epoch: int):
        """设置掩码策略的当前轮数 (用于渐进式掩码)"""
        if hasattr(self.masking_strategy, 'set_epoch'):
            self.masking_strategy.set_epoch(epoch)


# 注册MAE模型
EncoderRegistry.register('mcg_mae', MCGMAE)


def create_mae_model(config: Dict[str, Any]) -> MCGMAE:
    """创建MAE模型的便利函数"""
    return MCGMAE(config)


if __name__ == "__main__":
    # 测试MAE模型
    config = {
        'encoder': {
            'hidden_dim': 512,
            'spatial_encoder': {
                'type': 'mcg_spatial',
                'conv_channels': [64, 128, 256]
            },
            'temporal_encoder': {
                'type': 'mcg_temporal',
                'd_model': 256,
                'nhead': 8,
                'num_layers': 4
            },
            'fusion_module': {
                'fusion_type': 'cross_attention',
                'hidden_dim': 512
            }
        },
        'decoder': {
            'decoder_dim': 256,
            'num_layers': 2
        },
        'masking': {
            'type': 'temporal_block',
            'mask_ratio': 0.25,
            'block_size_range': [50, 100]
        },
        'loss_type': 'mse',
        'normalize_target': True,
        'mask_loss_only': True
    }
    
    # 创建模型
    mae_model = create_mae_model(config)
    
    print(f"MAE模型参数量: {sum(p.numel() for p in mae_model.parameters())}")
    print(f"编码器参数量: {sum(p.numel() for p in mae_model.encoder.parameters())}")
    print(f"解码器参数量: {sum(p.numel() for p in mae_model.decoder.parameters())}")
    
    # 测试前向传播
    batch_size, seq_len, channels = 4, 1500, 36
    x = torch.randn(batch_size, seq_len, channels)
    
    # 前向传播
    result = mae_model(x, return_encoder_output=True)
    
    print(f"\\n输入形状: {x.shape}")
    print(f"重建形状: {result['reconstructed'].shape}")
    print(f"掩码形状: {result['mask'].shape}")
    print(f"重建损失: {result['total_loss'].item():.6f}")
    print(f"实际掩码比例: {(1 - result['mask'].mean()).item():.2%}")
    
    # 测试编码功能
    encoder_output = mae_model.encode(x)
    print(f"编码器特征形状: {encoder_output.features.shape}")
    print(f"中间特征数量: {len(encoder_output.intermediate_features)}")
    
    print("\\nMAE模型测试完成！")