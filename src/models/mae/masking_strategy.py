"""
MAE掩码策略实现
==============

完全可配置的掩码策略系统，支持：

基础策略：
- 时间块掩码：掩盖连续时间段（所有通道的同一时间区间）
- 空间块掩码：掩盖6x6网格中的相邻通道区域
- 联合时空掩码：同时在时间和空间维度创建块状掩码

高级策略：
- 结构化掩码：基于MCG信号的生理结构设计
- 自适应掩码：根据信号特征动态调整掩码模式
- 渐进式掩码：训练过程中逐步增加掩码难度

所有策略参数完全可配置，便于实验比较
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from abc import ABC, abstractmethod
import random
import math

import logging
logger = logging.getLogger(__name__)


class MaskingStrategy(ABC):
    """掩码策略抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mask_ratio = config.get('mask_ratio', 0.2)
        self.seed = config.get('seed', None)
    
    @abstractmethod
    def generate_mask(self, batch_size: int, seq_len: int, num_channels: int) -> torch.Tensor:
        """
        生成掩码
        
        Args:
            batch_size: 批大小
            seq_len: 序列长度
            num_channels: 通道数 (36)
            
        Returns:
            掩码张量 [batch, seq_len, num_channels], 1=保留, 0=掩码
        """
        pass
    
    def set_seed(self, seed: Optional[int] = None):
        """设置随机种子"""
        if seed is not None:
            self.seed = seed
        if self.seed is not None:
            torch.manual_seed(self.seed)
            np.random.seed(self.seed)
            random.seed(self.seed)


class TemporalBlockMasking(MaskingStrategy):
    """时间块掩码 - 掩盖连续时间段"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 可配置参数
        self.block_size_range = config.get('block_size_range', [20, 100])  # 时间块大小范围
        self.num_blocks_range = config.get('num_blocks_range', [1, 3])     # 块数量范围
        self.allow_overlap = config.get('allow_overlap', False)            # 是否允许重叠
        self.edge_preserve = config.get('edge_preserve', True)             # 是否保护边缘
        self.min_preserve_ratio = config.get('min_preserve_ratio', 0.1)    # 最小保留比例
    
    def generate_mask(self, batch_size: int, seq_len: int, num_channels: int) -> torch.Tensor:
        """生成时间块掩码"""
        self.set_seed()
        
        masks = []
        
        for _ in range(batch_size):
            mask = torch.ones(seq_len, num_channels, dtype=torch.float32)
            
            # 计算目标掩码时间点数
            target_masked_points = int(seq_len * self.mask_ratio)
            current_masked_points = 0
            
            # 随机块数量
            num_blocks = np.random.randint(self.num_blocks_range[0], self.num_blocks_range[1] + 1)
            
            # 边缘保护区域
            edge_buffer = 50 if self.edge_preserve else 0
            valid_start_range = [edge_buffer, seq_len - edge_buffer]
            
            blocks = []
            for _ in range(num_blocks):
                if current_masked_points >= target_masked_points:
                    break
                
                # 随机块大小
                remaining_points = target_masked_points - current_masked_points
                max_block_size = min(self.block_size_range[1], remaining_points)
                min_block_size = min(self.block_size_range[0], max_block_size)
                
                if max_block_size < min_block_size:
                    break
                
                block_size = np.random.randint(min_block_size, max_block_size + 1)
                
                # 选择起始位置
                max_attempts = 50
                for attempt in range(max_attempts):
                    start_pos = np.random.randint(
                        valid_start_range[0], 
                        min(valid_start_range[1], seq_len - block_size + 1)
                    )
                    end_pos = start_pos + block_size
                    
                    # 检查重叠
                    if not self.allow_overlap:
                        overlap = any(
                            not (end_pos <= block['start'] or start_pos >= block['end'])
                            for block in blocks
                        )
                        if overlap:
                            continue
                    
                    # 应用掩码
                    mask[start_pos:end_pos, :] = 0  # 掩盖所有通道
                    blocks.append({'start': start_pos, 'end': end_pos})
                    current_masked_points += block_size
                    break
            
            # 检查最小保留比例
            actual_preserve_ratio = mask.mean().item()
            if actual_preserve_ratio < self.min_preserve_ratio:
                # 随机恢复一些点
                masked_positions = (mask == 0).nonzero()
                if len(masked_positions) > 0:
                    num_restore = int((self.min_preserve_ratio - actual_preserve_ratio) * seq_len * num_channels)
                    restore_indices = np.random.choice(len(masked_positions), 
                                                     min(num_restore, len(masked_positions)), 
                                                     replace=False)
                    for idx in restore_indices:
                        t, c = masked_positions[idx]
                        mask[t, c] = 1
            
            masks.append(mask)
        
        return torch.stack(masks)


class SpatialBlockMasking(MaskingStrategy):
    """空间块掩码 - 掩盖6x6网格中的相邻区域"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 可配置参数
        self.block_shapes = config.get('block_shapes', [(2, 2), (3, 3), (2, 3)])  # 块形状选项
        self.num_blocks_range = config.get('num_blocks_range', [1, 4])             # 块数量范围
        self.allow_overlap = config.get('allow_overlap', False)                    # 是否允许重叠
        self.time_mode = config.get('time_mode', 'all')  # 时间处理模式：'all', 'random_segments', 'fixed_segments'
        self.time_segment_ratio = config.get('time_segment_ratio', 0.3)            # 时间段比例
        self.channel_mapping = self._create_channel_mapping()
    
    def _create_channel_mapping(self) -> np.ndarray:
        """创建36通道到6x6网格的映射"""
        return np.arange(36).reshape(6, 6)
    
    def generate_mask(self, batch_size: int, seq_len: int, num_channels: int) -> torch.Tensor:
        """生成空间块掩码"""
        self.set_seed()
        
        masks = []
        
        for _ in range(batch_size):
            mask = torch.ones(seq_len, num_channels, dtype=torch.float32)
            
            # 确定时间范围
            if self.time_mode == 'all':
                time_indices = list(range(seq_len))
            elif self.time_mode == 'random_segments':
                segment_length = int(seq_len * self.time_segment_ratio)
                start_time = np.random.randint(0, max(1, seq_len - segment_length))
                time_indices = list(range(start_time, min(seq_len, start_time + segment_length)))
            elif self.time_mode == 'fixed_segments':
                # 固定的几个时间段
                num_segments = 3
                segment_length = seq_len // num_segments
                selected_segment = np.random.randint(num_segments)
                start_time = selected_segment * segment_length
                end_time = min(seq_len, start_time + segment_length)
                time_indices = list(range(start_time, end_time))
            else:
                time_indices = list(range(seq_len))
            
            # 计算目标掩码通道数
            target_masked_channels = int(num_channels * self.mask_ratio)
            
            # 随机块数量
            num_blocks = np.random.randint(self.num_blocks_range[0], self.num_blocks_range[1] + 1)
            
            used_positions = set()
            current_masked_channels = 0
            
            for _ in range(num_blocks):
                if current_masked_channels >= target_masked_channels:
                    break
                
                # 随机选择块形状
                block_h, block_w = random.choice(self.block_shapes)
                
                # 随机选择起始位置
                max_attempts = 50
                for _ in range(max_attempts):
                    start_row = np.random.randint(0, 7 - block_h)  # 6x6网格
                    start_col = np.random.randint(0, 7 - block_w)
                    
                    # 获取块内的所有位置
                    block_positions = []
                    for r in range(start_row, start_row + block_h):
                        for c in range(start_col, start_col + block_w):
                            block_positions.append((r, c))
                    
                    # 检查重叠
                    if not self.allow_overlap:
                        if any(pos in used_positions for pos in block_positions):
                            continue
                    
                    # 应用掩码
                    for r, c in block_positions:
                        channel_idx = self.channel_mapping[r, c]
                        for t in time_indices:
                            mask[t, channel_idx] = 0
                        used_positions.add((r, c))
                        current_masked_channels += 1
                    
                    break
            
            masks.append(mask)
        
        return torch.stack(masks)


class JointSpatioTemporalMasking(MaskingStrategy):
    """联合时空掩码 - 同时在时间和空间维度创建块状掩码"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 可配置参数
        self.temporal_ratio = config.get('temporal_ratio', 0.6)    # 时间掩码占比
        self.spatial_ratio = config.get('spatial_ratio', 0.4)     # 空间掩码占比
        
        # 时间块配置
        self.temporal_config = config.get('temporal_config', {
            'block_size_range': [30, 80],
            'num_blocks_range': [1, 2],
            'allow_overlap': False
        })
        
        # 空间块配置
        self.spatial_config = config.get('spatial_config', {
            'block_shapes': [(2, 2), (3, 2)],
            'num_blocks_range': [1, 3],
            'allow_overlap': False,
            'time_mode': 'random_segments'
        })
        
        # 创建子策略
        temporal_config = {**config, **self.temporal_config, 'mask_ratio': self.mask_ratio * self.temporal_ratio}
        spatial_config = {**config, **self.spatial_config, 'mask_ratio': self.mask_ratio * self.spatial_ratio}
        
        self.temporal_masker = TemporalBlockMasking(temporal_config)
        self.spatial_masker = SpatialBlockMasking(spatial_config)
    
    def generate_mask(self, batch_size: int, seq_len: int, num_channels: int) -> torch.Tensor:
        """生成联合时空掩码"""
        self.set_seed()
        
        # 生成时间掩码和空间掩码
        temporal_masks = self.temporal_masker.generate_mask(batch_size, seq_len, num_channels)
        spatial_masks = self.spatial_masker.generate_mask(batch_size, seq_len, num_channels)
        
        # 联合掩码：两种掩码的交集（都被掩码的位置才被掩码）
        # 这样可以保持合理的掩码比例
        joint_masks = temporal_masks * spatial_masks
        
        return joint_masks


class StructuredMasking(MaskingStrategy):
    """结构化掩码 - 基于MCG信号的生理结构"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 生理结构区域定义（可配置）
        self.anatomical_regions = config.get('anatomical_regions', {
            'anterior': [0, 1, 2, 6, 7, 8],      # 前壁导联
            'lateral': [3, 4, 5, 9, 10, 11],     # 侧壁导联  
            'inferior': [18, 19, 20, 24, 25, 26], # 下壁导联
            'posterior': [21, 22, 23, 27, 28, 29] # 后壁导联
        })
        
        self.region_mask_prob = config.get('region_mask_prob', 0.3)  # 每个区域被掩码的概率
        self.within_region_ratio = config.get('within_region_ratio', 0.8)  # 区域内掩码比例
        self.time_coherence = config.get('time_coherence', True)  # 是否保持时间连贯性
        
    def generate_mask(self, batch_size: int, seq_len: int, num_channels: int) -> torch.Tensor:
        """生成结构化掩码"""
        self.set_seed()
        
        masks = []
        
        for _ in range(batch_size):
            mask = torch.ones(seq_len, num_channels, dtype=torch.float32)
            
            # 随机选择要掩码的解剖区域
            for region_name, channels in self.anatomical_regions.items():
                if np.random.random() < self.region_mask_prob:
                    # 在区域内随机选择通道
                    available_channels = [ch for ch in channels if ch < num_channels]
                    num_mask_channels = int(len(available_channels) * self.within_region_ratio)
                    masked_channels = np.random.choice(
                        available_channels, 
                        size=min(num_mask_channels, len(available_channels)), 
                        replace=False
                    )
                    
                    if self.time_coherence:
                        # 时间连贯性：掩码连续时间段
                        block_size = np.random.randint(50, 200)
                        start_time = np.random.randint(0, max(1, seq_len - block_size))
                        end_time = min(seq_len, start_time + block_size)
                        
                        for ch in masked_channels:
                            mask[start_time:end_time, ch] = 0
                    else:
                        # 随机时间点
                        time_mask_ratio = 0.3
                        num_time_points = int(seq_len * time_mask_ratio)
                        masked_times = np.random.choice(
                            seq_len, size=num_time_points, replace=False
                        )
                        
                        for ch in masked_channels:
                            mask[masked_times, ch] = 0
            
            masks.append(mask)
        
        return torch.stack(masks)


class AdaptiveMasking(MaskingStrategy):
    """自适应掩码 - 根据信号特征动态调整"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        self.adaptation_method = config.get('adaptation_method', 'energy_based')  # 'energy_based', 'variance_based'
        self.high_energy_mask_prob = config.get('high_energy_mask_prob', 0.4)    # 高能量区域掩码概率
        self.low_energy_mask_prob = config.get('low_energy_mask_prob', 0.1)      # 低能量区域掩码概率
        self.energy_percentile = config.get('energy_percentile', 0.7)            # 能量分位点
        
    def calculate_signal_energy(self, signal: torch.Tensor) -> torch.Tensor:
        """计算信号能量"""
        # signal: [seq_len, num_channels]
        energy = signal.pow(2).sum(dim=0)  # [num_channels]
        return energy
    
    def generate_mask(self, batch_size: int, seq_len: int, num_channels: int, 
                     signals: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        生成自适应掩码
        
        Args:
            signals: 原始信号 [batch, seq_len, num_channels] (用于自适应)
        """
        self.set_seed()
        
        if signals is None:
            # 如果没有信号，退回到随机掩码
            logger.warning("自适应掩码缺少信号输入，使用随机掩码")
            masks = torch.rand(batch_size, seq_len, num_channels) > self.mask_ratio
            return masks.float()
        
        masks = []
        
        for i in range(batch_size):
            signal = signals[i]  # [seq_len, num_channels]
            mask = torch.ones(seq_len, num_channels, dtype=torch.float32)
            
            if self.adaptation_method == 'energy_based':
                # 基于能量的自适应
                energy = self.calculate_signal_energy(signal)
                energy_threshold = torch.quantile(energy, self.energy_percentile)
                
                for ch in range(num_channels):
                    if energy[ch] > energy_threshold:
                        # 高能量通道，更容易被掩码
                        mask_prob = self.high_energy_mask_prob
                    else:
                        # 低能量通道，较少被掩码
                        mask_prob = self.low_energy_mask_prob
                    
                    # 随机时间点掩码
                    time_mask = torch.rand(seq_len) < mask_prob
                    mask[time_mask, ch] = 0
            
            elif self.adaptation_method == 'variance_based':
                # 基于方差的自适应
                variance = signal.var(dim=0)  # [num_channels]
                var_threshold = torch.quantile(variance, self.energy_percentile)
                
                for ch in range(num_channels):
                    if variance[ch] > var_threshold:
                        mask_prob = self.high_energy_mask_prob
                    else:
                        mask_prob = self.low_energy_mask_prob
                    
                    time_mask = torch.rand(seq_len) < mask_prob
                    mask[time_mask, ch] = 0
            
            masks.append(mask)
        
        return torch.stack(masks)


class ProgressiveMasking(MaskingStrategy):
    """渐进式掩码 - 训练过程中逐步增加难度"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        self.initial_ratio = config.get('initial_ratio', 0.1)      # 初始掩码比例
        self.final_ratio = config.get('final_ratio', 0.3)         # 最终掩码比例  
        self.warmup_epochs = config.get('warmup_epochs', 20)      # 预热轮数
        self.total_epochs = config.get('total_epochs', 100)       # 总轮数
        
        self.current_epoch = 0
        self.base_strategy_config = config.get('base_strategy', {'type': 'temporal_block'})
        
    def set_epoch(self, epoch: int):
        """设置当前轮数"""
        self.current_epoch = epoch
        
        # 计算当前掩码比例
        if epoch < self.warmup_epochs:
            progress = epoch / self.warmup_epochs
        else:
            progress = 1.0
        
        self.mask_ratio = self.initial_ratio + (self.final_ratio - self.initial_ratio) * progress
    
    def generate_mask(self, batch_size: int, seq_len: int, num_channels: int) -> torch.Tensor:
        """生成渐进式掩码"""
        # 更新基础策略的掩码比例
        base_config = {**self.base_strategy_config, 'mask_ratio': self.mask_ratio}
        
        # 根据策略类型创建掩码器
        strategy_type = base_config.get('type', 'temporal_block')
        
        if strategy_type == 'temporal_block':
            masker = TemporalBlockMasking(base_config)
        elif strategy_type == 'spatial_block':
            masker = SpatialBlockMasking(base_config)
        elif strategy_type == 'joint_spatiotemporal':
            masker = JointSpatioTemporalMasking(base_config)
        else:
            masker = TemporalBlockMasking(base_config)
        
        return masker.generate_mask(batch_size, seq_len, num_channels)


# 掩码策略注册器
class MaskingRegistry:
    """掩码策略注册器"""
    
    _strategies = {
        'temporal_block': TemporalBlockMasking,
        'spatial_block': SpatialBlockMasking,
        'joint_spatiotemporal': JointSpatioTemporalMasking,
        'structured': StructuredMasking,
        'adaptive': AdaptiveMasking,
        'progressive': ProgressiveMasking
    }
    
    @classmethod
    def register(cls, name: str, strategy_class: type):
        """注册掩码策略"""
        cls._strategies[name] = strategy_class
    
    @classmethod
    def get_strategy(cls, name: str) -> type:
        """获取掩码策略类"""
        if name not in cls._strategies:
            raise ValueError(f"未注册的掩码策略: {name}")
        return cls._strategies[name]
    
    @classmethod
    def create_strategy(cls, config: Dict[str, Any]) -> MaskingStrategy:
        """创建掩码策略实例"""
        strategy_type = config.get('type', 'temporal_block')
        strategy_class = cls.get_strategy(strategy_type)
        return strategy_class(config)


def create_masking_strategy(config: Dict[str, Any]) -> MaskingStrategy:
    """创建掩码策略的便利函数"""
    return MaskingRegistry.create_strategy(config)


if __name__ == "__main__":
    # 测试掩码策略
    batch_size, seq_len, num_channels = 4, 1500, 36
    
    # 测试不同策略
    strategies_configs = [
        {
            'type': 'temporal_block',
            'mask_ratio': 0.2,
            'block_size_range': [50, 100],
            'num_blocks_range': [1, 3]
        },
        {
            'type': 'spatial_block', 
            'mask_ratio': 0.15,
            'block_shapes': [(2, 2), (3, 3)],
            'time_mode': 'random_segments'
        },
        {
            'type': 'joint_spatiotemporal',
            'mask_ratio': 0.25,
            'temporal_ratio': 0.6,
            'spatial_ratio': 0.4
        },
        {
            'type': 'structured',
            'mask_ratio': 0.2,
            'region_mask_prob': 0.4,
            'time_coherence': True
        }
    ]
    
    for config in strategies_configs:
        strategy = create_masking_strategy(config)
        mask = strategy.generate_mask(batch_size, seq_len, num_channels)
        
        actual_mask_ratio = 1 - mask.mean().item()
        print(f"{config['type']} 掩码:")
        print(f"  掩码形状: {mask.shape}")
        print(f"  目标掩码比例: {config['mask_ratio']:.2%}")
        print(f"  实际掩码比例: {actual_mask_ratio:.2%}")
        print()
    
    # 测试自适应掩码（需要信号输入）
    signals = torch.randn(batch_size, seq_len, num_channels)
    adaptive_config = {
        'type': 'adaptive',
        'mask_ratio': 0.2,
        'adaptation_method': 'energy_based'
    }
    
    adaptive_strategy = create_masking_strategy(adaptive_config)
    adaptive_mask = adaptive_strategy.generate_mask(batch_size, seq_len, num_channels, signals)
    
    print(f"自适应掩码比例: {1 - adaptive_mask.mean().item():.2%}")
    
    print("掩码策略测试完成！")