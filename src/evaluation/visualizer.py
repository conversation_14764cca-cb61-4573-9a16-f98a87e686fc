"""
MCG可视化模块
=============

基于原始MCGVisualizer扩展，为MCG三阶段项目提供全面的可视化分析工具：

原始功能：
1. 时空波组图 (6x6网格)
2. 时间波组图 (叠加显示)
3. 插值磁图 (空间热力图)
4. 对比分析和相似度计算

MAE扩展功能：
5. 掩码可视化
6. 重建质量分析
7. 特征表征可视化
8. 训练进度监控
9. 模型解释性分析
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
import seaborn as sns
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
import torch
from scipy.stats import pearsonr
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import pandas as pd
import warnings

import logging
logger = logging.getLogger(__name__)

# 设置绘图样式
plt.rcParams['font.family'] = ['DejaVu Sans', 'SimHei']  # 支持中文
plt.rcParams['axes.unicode_minus'] = False


class MCGVisualizer:
    """
    MCG可视化工具类 - 基于原始工具扩展
    
    支持原始功能和MAE项目扩展功能
    """
    
    def __init__(self, data1, data2=None, reference_channel=None,
                 data1_name='Data 1', data2_name='Data 2',
                 show_stacked_diff=True, show_reference_channel=True):
        """
        初始化Visualizer
        
        Args:
            data1: 主要数据 [seq_len, 36]
            data2: 对比数据 [seq_len, 36] (可选)
            reference_channel: 参考通道 [seq_len] (可选)
            data1_name, data2_name: 数据名称
            show_stacked_diff: 是否显示差异图
            show_reference_channel: 是否显示参考通道
        """
        # 数据校验与存储
        if data1.shape[1] != 36:
            raise ValueError("输入数据的第二维必须是36 (通道数)")
        self.data1 = data1
        self.data1_6x6 = self.data1.reshape(self.data1.shape[0], 6, 6)
        self.data1_name = data1_name

        self.data2 = None
        self.diff_data = None
        if data2 is not None:
            if data1.shape != data2.shape:
                raise ValueError("对比数据 data1 和 data2 必须有相同的形状")
            self.data2 = data2
            self.data2_6x6 = self.data2.reshape(self.data2.shape[0], 6, 6)
            self.diff_data = self.data1 - self.data2
        self.data2_name = data2_name

        self.show_stacked_diff = show_stacked_diff and (self.data2 is not None)
        self.show_reference_channel = show_reference_channel and (reference_channel is not None)

        # 计算统一的Y轴范围
        all_data_list = [self.data1]
        if self.data2 is not None: 
            all_data_list.append(self.data2)
        if self.diff_data is not None and self.show_stacked_diff: 
            all_data_list.append(self.diff_data)

        all_data = np.concatenate(all_data_list, axis=0)
        data_min = np.min(all_data)
        data_max = np.max(all_data)
        padding = (data_max - data_min) * 0.1
        self.ylim = (data_min - padding, data_max + padding)

        # 与参考通道计算相似度
        self.reference_channel = reference_channel
        self.similarity_results = {}
        if self.reference_channel is not None:
            self._calculate_similarity_metrics()
    
    def _calculate_similarity_metrics(self):
        """计算每个通道与参考通道的相似度，并计算平均值"""
        if self.reference_channel is None: 
            return

        def compute_metrics(data, data_name):
            metrics = {}
            correlations = []
            for i in range(36):
                min_len = min(len(data[:, i]), len(self.reference_channel))
                channel_data = data[:min_len, i]
                ref_data = self.reference_channel[:min_len]
                corr, _ = pearsonr(channel_data, ref_data)
                mse = np.mean((channel_data - ref_data) ** 2)
                metrics[i] = {'correlation': corr, 'mse': mse}
                correlations.append(corr)

            # 计算平均相似度 (使用绝对值的平均值)
            self.similarity_results[data_name] = {
                'channels': metrics,
                'mean_correlation': np.mean(np.abs(correlations))
            }

        compute_metrics(self.data1, self.data1_name)
        if self.data2 is not None:
            compute_metrics(self.data2, self.data2_name)
    
    def _plot_single_grid(self, fig, axes, data, title, data_name, 
                         show_similarity=True, data2=None, data2_name=None):
        """辅助函数：在给定的 axes 网格上绘制数据"""
        sim_data = self.similarity_results.get(data_name, {})
        sim_data2 = self.similarity_results.get(data2_name, {}) if data2_name else {}
        primary_color = '#00529B' if data_name != 'Difference' else '#D81B60'
        secondary_color = '#D81B60' if data2 is not None else None

        for i, ax in enumerate(axes.flatten()):
            # 绘制第一组数据
            ax.plot(data[:, i], color=primary_color, linewidth=1.2, 
                   label=data_name if data2 is not None else None)

            # 如果有第二组数据，堆叠显示
            if data2 is not None:
                ax.plot(data2[:, i], color=secondary_color, linewidth=1.2, 
                       alpha=0.8, label=data2_name)
                if i == 0:  # 只在第一个子图添加图例
                    ax.legend(loc='upper right', fontsize=6)

            ax.grid(True, linestyle='--', alpha=0.5)

            # 在左上角标注通道号
            ax.text(0.05, 0.95, f'Ch{i + 1}', transform=ax.transAxes, fontsize=8,
                    verticalalignment='top', 
                    bbox=dict(boxstyle='round,pad=0.2', fc='white', alpha=0.7, ec='none'))

            # 如果有相似度结果，则在右上角标注
            if show_similarity and sim_data and 'channels' in sim_data:
                corr = sim_data['channels'][i]['correlation']
                corr_text = f'R1={corr:.2f}'

                # 如果有第二组数据的相似度，也显示
                if sim_data2 and 'channels' in sim_data2:
                    corr2 = sim_data2['channels'][i]['correlation']
                    corr_text += f'\\nR2={corr2:.2f}'

                ax.text(0.95, 0.95, corr_text, transform=ax.transAxes, fontsize=7,
                        horizontalalignment='right', verticalalignment='top',
                        bbox=dict(boxstyle='round,pad=0.2', fc='lightgoldenrodyellow', 
                                 alpha=0.8, ec='none'))

        # 添加大标题
        title_text = title
        if show_similarity and sim_data and 'mean_correlation' in sim_data:
            mean_corr = sim_data['mean_correlation']
            title_text += f'\\n(Avg. Correlation: {mean_corr:.3f})'

            # 如果有第二组数据的平均相似度，也显示
            if sim_data2 and 'mean_correlation' in sim_data2:
                mean_corr2 = sim_data2['mean_correlation']
                title_text += f' | {data2_name}: {mean_corr2:.3f}'

        axes[0, 0].set_title(title_text, fontsize=16, loc='left', x=-0.1, y=1.25)
    
    def plot_spatiotemporal_grid(self, suptitle="时空波组图", stack=False, savefile=None):
        """绘制6x6的时空波组图，支持对比和堆叠差异显示"""
        # 根据stack参数决定布局
        if stack and self.data2 is not None:
            # 堆叠模式：只需要一列，两组数据在同一子图中显示
            num_rows, num_cols = 6, 6 
            fig_width = 20 
            fig_height = 15
            fig, all_axes = plt.subplots(num_rows, num_cols, figsize=(fig_width, fig_height), 
                                       sharex=True, sharey=True)
            fig.suptitle(suptitle, fontsize=22, y=0.98)

            # 调整Y轴范围
            plt.ylim(self.ylim)

            # 堆叠显示两组数据
            self._plot_single_grid(fig, all_axes, self.data1, f"{self.data1_name} vs {self.data2_name}",
                                 self.data1_name, show_similarity=True,
                                 data2=self.data2, data2_name=self.data2_name)
        else:
            # 原有的并排显示模式
            num_cols = 1
            if self.data2 is not None:
                num_cols = 3 if self.show_stacked_diff else 2

            fig_width = 10 * num_cols
            fig, all_axes = plt.subplots(6, 6 * num_cols, figsize=(fig_width, 10), 
                                       sharex=True, sharey=True)
            fig.suptitle(suptitle, fontsize=22, y=0.98)

            # 调整Y轴范围
            plt.ylim(self.ylim)

            # 绘制第一组数据
            axes1 = all_axes[:, :6] if num_cols > 1 else all_axes
            self._plot_single_grid(fig, axes1, self.data1, self.data1_name, self.data1_name)

            if self.data2 is not None:
                # 绘制第二组数据
                axes2 = all_axes[:, 6:12]
                self._plot_single_grid(fig, axes2, self.data2, self.data2_name, self.data2_name)

                if self.show_stacked_diff:
                    # 绘制差异图
                    axes3 = all_axes[:, 12:18]
                    self._plot_single_grid(fig, axes3, self.diff_data, 'Difference', 
                                         'Difference', show_similarity=False)

        plt.tight_layout(rect=[0, 0, 1, 0.93])

        # 根据savefile参数决定是保存还是显示
        if savefile:
            plt.savefig(savefile, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.show()
    
    def _plot_single_overlay(self, ax, data, title, data_name):
        """辅助函数：在指定的Axes上绘制一个叠加图"""
        colors = plt.cm.viridis(np.linspace(0, 1, 36))
        for i in range(36):
            ax.plot(data[:, i], color=colors[i], alpha=0.7, label=f'Ch {i + 1}')
        ax.grid(True, linestyle='--', alpha=0.6)

        title_text = title
        sim_data = self.similarity_results.get(data_name)
        if sim_data and 'mean_correlation' in sim_data:
            mean_corr = sim_data['mean_correlation']
            title_text += f'\\n(Avg. Correlation: {mean_corr:.3f})'
        ax.set_title(title_text, fontsize=16)

        ax.set_xlabel("Time Points", fontsize=12)
        ax.set_ylabel("Amplitude", fontsize=12)
        return ax
    
    def plot_temporal_overlay(self, suptitle="时间波组图", savefile=None):
        """绘制叠加图，支持对比、堆叠差异和显示参考通道"""
        num_cols = 1
        if self.data2 is not None:
            num_cols = 3 if self.show_stacked_diff else 2

        num_rows = 2 if self.show_reference_channel else 1
        height_ratios = [4, 1] if self.show_reference_channel else [1]

        fig = plt.figure(figsize=(8 * num_cols, 6 * num_rows))
        gs = gridspec.GridSpec(num_rows, num_cols, figure=fig, 
                              height_ratios=height_ratios, hspace=0.4)
        fig.suptitle(suptitle, fontsize=20)

        # 绘制主图
        ax1 = self._plot_single_overlay(fig.add_subplot(gs[0, 0]), self.data1, 
                                       self.data1_name, self.data1_name)
        ax1.set_ylim(self.ylim)

        if self.data2 is not None:
            ax2 = self._plot_single_overlay(fig.add_subplot(gs[0, 1], sharey=ax1), 
                                           self.data2, self.data2_name, self.data2_name)
            plt.setp(ax2.get_yticklabels(), visible=False)
            ax2.set_ylabel("")

            if self.show_stacked_diff:
                ax3 = self._plot_single_overlay(fig.add_subplot(gs[0, 2], sharey=ax1), 
                                               self.diff_data, 'Difference', 'Difference')
                plt.setp(ax3.get_yticklabels(), visible=False)
                ax3.set_ylabel("")

        # 绘制参考通道
        if self.show_reference_channel:
            ref_ax = fig.add_subplot(gs[1, :])  # 跨越所有列
            ref_ax.plot(self.reference_channel, color='black', linewidth=1.5)
            ref_ax.set_title("Reference Channel", fontsize=14)
            ref_ax.set_xlabel("Time Points", fontsize=12)
            ref_ax.set_ylabel("Amplitude", fontsize=12)
            ref_ax.grid(True, linestyle='--', alpha=0.6)
            ref_ax.autoscale(enable=True, axis='x', tight=True)

        plt.tight_layout(rect=[0, 0, 1, 0.95])

        if savefile:
            plt.savefig(savefile, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.show()
    
    def _plot_single_map(self, ax, data_6x6, time_point, title):
        """辅助函数：绘制单个插值磁图"""
        num_samples = data_6x6.shape[0]
        if time_point is None:
            time_point = np.unravel_index(np.argmax(np.abs(data_6x6)), data_6x6.shape)[0]
        time_point = max(0, min(time_point, num_samples - 1))

        data_2d = data_6x6[time_point, :, :]
        data_interpolated = cv2.resize(data_2d, (200, 200), interpolation=cv2.INTER_CUBIC)
        vmax = np.max(np.abs(data_interpolated))

        im = ax.imshow(data_interpolated, cmap='coolwarm', vmin=-vmax, vmax=vmax, origin='lower')
        ax.set_title(f"{title}\\nTime Point: {time_point}", fontsize=14)
        ax.set_xticks([])
        ax.set_yticks([])
        return im, vmax
    
    def plot_interpolated_map(self, time_point=None, suptitle="插值磁图", savefile=None):
        """绘制插值磁图，修复了colorbar重叠问题"""
        if self.data2 is None:
            fig, ax = plt.subplots(figsize=(7, 6))
            im, _ = self._plot_single_map(ax, self.data1_6x6, time_point, self.data1_name)
            fig.colorbar(im, ax=ax, label='Amplitude', fraction=0.046, pad=0.04)
        else:
            fig, axes = plt.subplots(1, 2, figsize=(12, 6))
            im1, vmax1 = self._plot_single_map(axes[0], self.data1_6x6, time_point, self.data1_name)
            im2, vmax2 = self._plot_single_map(axes[1], self.data2_6x6, time_point, self.data2_name)

            vmax_global = max(vmax1, vmax2) if vmax1 > 0 and vmax2 > 0 else max(abs(vmax1), abs(vmax2))
            im1.set_clim(-vmax_global, vmax_global)
            im2.set_clim(-vmax_global, vmax_global)

            # 调整colorbar的位置，避免重叠
            fig.subplots_adjust(right=0.85)
            cbar_ax = fig.add_axes([0.88, 0.15, 0.04, 0.7])
            fig.colorbar(im2, cax=cbar_ax, label='Amplitude')

        fig.suptitle(suptitle, fontsize=20, y=0.98)

        if savefile:
            plt.savefig(savefile, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.show()


class MAEVisualizer(MCGVisualizer):
    """
    MAE专用可视化工具 - 扩展原始功能
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    
    def plot_masking_visualization(self, original_signal, masked_signal, mask,
                                  suptitle="MAE掩码可视化", savefile=None):
        """
        可视化MAE掩码效果
        
        Args:
            original_signal: 原始信号 [seq_len, 36]
            masked_signal: 掩码后信号 [seq_len, 36]
            mask: 掩码 [seq_len, 36] (1=保留, 0=掩码)
            suptitle: 图标题
            savefile: 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(suptitle, fontsize=20)
        
        # 原始信号 (选择几个通道显示)
        channels_to_show = [0, 9, 18, 27]  # 4个代表性通道
        for i, ch in enumerate(channels_to_show):
            axes[0, 0].plot(original_signal[:, ch], alpha=0.7, label=f'Ch{ch+1}')
        axes[0, 0].set_title('原始信号 (选择通道)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 掩码后信号
        for i, ch in enumerate(channels_to_show):
            axes[0, 1].plot(masked_signal[:, ch], alpha=0.7, label=f'Ch{ch+1}')
        axes[0, 1].set_title('掩码后信号 (选择通道)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 掩码模式 (时间-通道热力图)
        im1 = axes[1, 0].imshow(mask.T, aspect='auto', cmap='RdBu', vmin=0, vmax=1)
        axes[1, 0].set_title('掩码模式 (白色=保留, 蓝色=掩码)')
        axes[1, 0].set_xlabel('时间点')
        axes[1, 0].set_ylabel('通道')
        plt.colorbar(im1, ax=axes[1, 0])
        
        # 掩码统计
        mask_ratio = 1 - mask.mean()
        temporal_mask_ratio = 1 - mask.mean(axis=1)
        spatial_mask_ratio = 1 - mask.mean(axis=0)
        
        axes[1, 1].plot(temporal_mask_ratio, label='时间维掩码比例')
        axes[1, 1].axhline(mask_ratio, color='red', linestyle='--', 
                          label=f'总体掩码比例: {mask_ratio:.2%}')
        axes[1, 1].set_title('掩码比例统计')
        axes[1, 1].set_xlabel('时间点')
        axes[1, 1].set_ylabel('掩码比例')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if savefile:
            plt.savefig(savefile, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.show()
    
    def plot_reconstruction_comparison(self, original, reconstructed, mask=None,
                                     channels_to_show=None, suptitle="重建效果对比", 
                                     savefile=None):
        """
        可视化重建效果对比
        
        Args:
            original: 原始信号 [seq_len, 36]
            reconstructed: 重建信号 [seq_len, 36]
            mask: 掩码 (可选)
            channels_to_show: 要显示的通道列表
            suptitle: 图标题
            savefile: 保存路径
        """
        if channels_to_show is None:
            channels_to_show = [0, 9, 18, 27, 32, 35]  # 6个代表性通道
        
        n_channels = len(channels_to_show)
        fig, axes = plt.subplots(3, 2, figsize=(16, 12))
        fig.suptitle(suptitle, fontsize=20)
        
        # 计算重建误差
        reconstruction_error = np.mean((original - reconstructed) ** 2, axis=0)
        
        for i, ch in enumerate(channels_to_show[:6]):
            row = i // 2
            col = i % 2
            ax = axes[row, col]
            
            # 绘制原始和重建信号
            ax.plot(original[:, ch], label='原始', alpha=0.8, linewidth=1.5)
            ax.plot(reconstructed[:, ch], label='重建', alpha=0.8, linewidth=1.5)
            
            # 如果有掩码，标记掩码区域
            if mask is not None:
                masked_regions = ~mask[:, ch].astype(bool)
                if masked_regions.any():
                    ax.axvspan(0, len(original), alpha=0.1, color='red', 
                              label='掩码区域' if i == 0 else "")
                    for start, end in self._find_masked_segments(masked_regions):
                        ax.axvspan(start, end, alpha=0.2, color='red')
            
            # 计算并显示指标
            mse = np.mean((original[:, ch] - reconstructed[:, ch]) ** 2)
            corr, _ = pearsonr(original[:, ch], reconstructed[:, ch])
            
            ax.set_title(f'通道 {ch+1} (MSE: {mse:.4f}, Corr: {corr:.3f})')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if savefile:
            plt.savefig(savefile, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.show()
        
        return {
            'mse_per_channel': np.mean((original - reconstructed) ** 2, axis=0),
            'correlation_per_channel': [pearsonr(original[:, i], reconstructed[:, i])[0] 
                                       for i in range(original.shape[1])],
            'overall_mse': np.mean((original - reconstructed) ** 2)
        }
    
    def _find_masked_segments(self, masked_array):
        """找到掩码的连续段"""
        segments = []
        start = None
        for i, masked in enumerate(masked_array):
            if masked and start is None:
                start = i
            elif not masked and start is not None:
                segments.append((start, i))
                start = None
        if start is not None:
            segments.append((start, len(masked_array)))
        return segments
    
    def plot_feature_analysis(self, features, labels=None, method='tsne',
                             suptitle="特征表征分析", savefile=None):
        """
        可视化学习到的特征表征
        
        Args:
            features: 特征向量 [n_samples, feature_dim]
            labels: 标签 (可选)
            method: 降维方法 ['tsne', 'pca']
            suptitle: 图标题
            savefile: 保存路径
        """
        if isinstance(features, torch.Tensor):
            features = features.detach().cpu().numpy()
        
        # 降维
        if method == 'tsne':
            reducer = TSNE(n_components=2, random_state=42, perplexity=min(30, features.shape[0]-1))
        elif method == 'pca':
            reducer = PCA(n_components=2)
        else:
            raise ValueError(f"不支持的降维方法: {method}")
        
        features_2d = reducer.fit_transform(features)
        
        fig, axes = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle(suptitle, fontsize=20)
        
        # 2D散点图
        if labels is not None:
            scatter = axes[0].scatter(features_2d[:, 0], features_2d[:, 1], 
                                    c=labels, cmap='tab10', alpha=0.7)
            plt.colorbar(scatter, ax=axes[0])
            axes[0].set_title(f'{method.upper()} 可视化 (有标签)')
        else:
            axes[0].scatter(features_2d[:, 0], features_2d[:, 1], alpha=0.7)
            axes[0].set_title(f'{method.upper()} 可视化')
        
        axes[0].set_xlabel(f'{method.upper()} 1')
        axes[0].set_ylabel(f'{method.upper()} 2')
        axes[0].grid(True, alpha=0.3)
        
        # 特征分布直方图
        axes[1].hist(features.flatten(), bins=50, alpha=0.7, edgecolor='black')
        axes[1].set_title('特征值分布')
        axes[1].set_xlabel('特征值')
        axes[1].set_ylabel('频次')
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if savefile:
            plt.savefig(savefile, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.show()
        
        return features_2d
    
    def plot_training_progress(self, train_losses, val_losses=None, 
                              metrics=None, suptitle="训练进度", savefile=None):
        """
        可视化训练进度
        
        Args:
            train_losses: 训练损失列表
            val_losses: 验证损失列表 (可选)
            metrics: 其他指标字典 (可选)
            suptitle: 图标题
            savefile: 保存路径
        """
        n_subplots = 2 if metrics else 1
        fig, axes = plt.subplots(1, n_subplots, figsize=(8 * n_subplots, 6))
        if n_subplots == 1:
            axes = [axes]
        
        fig.suptitle(suptitle, fontsize=20)
        
        # 损失曲线
        epochs = range(1, len(train_losses) + 1)
        axes[0].plot(epochs, train_losses, label='训练损失', marker='o', markersize=3)
        
        if val_losses:
            axes[0].plot(epochs[:len(val_losses)], val_losses, 
                        label='验证损失', marker='s', markersize=3)
        
        axes[0].set_title('损失曲线')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        axes[0].set_yscale('log')
        
        # 其他指标
        if metrics and len(axes) > 1:
            for metric_name, values in metrics.items():
                if len(values) > 0:
                    metric_epochs = range(1, len(values) + 1)
                    axes[1].plot(metric_epochs, values, label=metric_name, 
                               marker='o', markersize=3)
            
            axes[1].set_title('评估指标')
            axes[1].set_xlabel('Epoch')
            axes[1].set_ylabel('Metric Value')
            axes[1].legend()
            axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if savefile:
            plt.savefig(savefile, dpi=300, bbox_inches='tight')
            plt.close()
        else:
            plt.show()


class MAEExperimentVisualizer:
    """MAE实验可视化工具 - 用于实验分析和报告生成"""
    
    def __init__(self, save_dir: Union[str, Path]):
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        (self.save_dir / 'experiments').mkdir(exist_ok=True)
        (self.save_dir / 'samples').mkdir(exist_ok=True)
        (self.save_dir / 'metrics').mkdir(exist_ok=True)
    
    def create_experiment_report(self, experiment_data: Dict[str, Any]):
        """
        创建完整的实验报告
        
        Args:
            experiment_data: 实验数据字典
                - train_losses: 训练损失
                - val_losses: 验证损失  
                - metrics: 评估指标
                - sample_results: 样本结果
                - config: 配置信息
        """
        report_dir = self.save_dir / 'experiments' / f"report_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}"
        report_dir.mkdir(exist_ok=True)
        
        # 1. 训练进度图
        mae_vis = MAEVisualizer(np.zeros((100, 36)))  # dummy data
        mae_vis.plot_training_progress(
            experiment_data.get('train_losses', []),
            experiment_data.get('val_losses', []),
            experiment_data.get('metrics', {}),
            savefile=str(report_dir / 'training_progress.png')
        )
        
        # 2. 样本重建对比
        if 'sample_results' in experiment_data:
            for i, sample_data in enumerate(experiment_data['sample_results'][:5]):  # 显示前5个样本
                mae_vis.plot_reconstruction_comparison(
                    sample_data['original'],
                    sample_data['reconstructed'],
                    sample_data.get('mask'),
                    suptitle=f"样本 {i+1} 重建对比",
                    savefile=str(report_dir / f'reconstruction_sample_{i+1}.png')
                )
        
        # 3. 配置和指标摘要
        self._create_summary_report(experiment_data, report_dir)
        
        logger.info(f"实验报告已生成: {report_dir}")
    
    def _create_summary_report(self, experiment_data: Dict, report_dir: Path):
        """创建文本摘要报告"""
        summary_path = report_dir / 'summary.txt'
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("MCG MAE实验摘要报告\\n")
            f.write("=" * 50 + "\\n\\n")
            
            # 配置信息
            if 'config' in experiment_data:
                f.write("实验配置:\\n")
                f.write("-" * 20 + "\\n")
                config = experiment_data['config']
                f.write(f"模型类型: {config.get('model_type', 'Unknown')}\\n")
                f.write(f"批大小: {config.get('batch_size', 'Unknown')}\\n")
                f.write(f"学习率: {config.get('learning_rate', 'Unknown')}\\n")
                f.write(f"训练轮数: {config.get('epochs', 'Unknown')}\\n\\n")
            
            # 最终指标
            f.write("最终性能指标:\\n")
            f.write("-" * 20 + "\\n")
            
            train_losses = experiment_data.get('train_losses', [])
            if train_losses:
                f.write(f"最终训练损失: {train_losses[-1]:.6f}\\n")
            
            val_losses = experiment_data.get('val_losses', [])
            if val_losses:
                f.write(f"最终验证损失: {val_losses[-1]:.6f}\\n")
            
            metrics = experiment_data.get('metrics', {})
            for metric_name, values in metrics.items():
                if values:
                    f.write(f"最终{metric_name}: {values[-1]:.6f}\\n")
            
            f.write("\\n报告生成时间: " + pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'))


# 便利函数
def create_mae_visualizer(original_signal: np.ndarray, 
                         reconstructed_signal: Optional[np.ndarray] = None,
                         reference_channel: Optional[np.ndarray] = None) -> MAEVisualizer:
    """创建MAE可视化器的便利函数"""
    return MAEVisualizer(
        original_signal, 
        reconstructed_signal, 
        reference_channel,
        data1_name='原始信号',
        data2_name='重建信号' if reconstructed_signal is not None else 'Data 2'
    )


def quick_mae_visualization(original: np.ndarray, 
                           reconstructed: np.ndarray,
                           mask: Optional[np.ndarray] = None,
                           save_dir: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
    """
    快速MAE可视化 - 一键生成所有常用图表
    
    Args:
        original: 原始信号
        reconstructed: 重建信号
        mask: 掩码 (可选)
        save_dir: 保存目录 (可选)
        
    Returns:
        重建质量指标字典
    """
    mae_vis = MAEVisualizer(original, reconstructed, 
                           data1_name='原始', data2_name='重建')
    
    save_path = lambda name: str(Path(save_dir) / f"{name}.png") if save_dir else None
    
    # 生成所有可视化
    mae_vis.plot_spatiotemporal_grid(
        suptitle="原始 vs 重建 - 时空波组图",
        savefile=save_path("spatiotemporal_comparison")
    )
    
    mae_vis.plot_temporal_overlay(
        suptitle="原始 vs 重建 - 时间叠加图", 
        savefile=save_path("temporal_overlay")
    )
    
    mae_vis.plot_interpolated_map(
        suptitle="原始 vs 重建 - 插值磁图",
        savefile=save_path("interpolated_map")
    )
    
    # 重建对比分析
    metrics = mae_vis.plot_reconstruction_comparison(
        original, reconstructed, mask,
        suptitle="重建质量详细分析",
        savefile=save_path("reconstruction_analysis")
    )
    
    if mask is not None:
        mae_vis.plot_masking_visualization(
            original, original * mask, mask,
            suptitle="掩码策略可视化",
            savefile=save_path("masking_visualization")
        )
    
    logger.info(f"MAE可视化完成，图表保存到: {save_dir}")
    return metrics


if __name__ == "__main__":
    # 测试代码
    # 生成测试数据
    seq_len, channels = 1500, 36
    original = np.random.randn(seq_len, channels) * 0.1
    
    # 模拟重建信号 (添加一些噪声)
    reconstructed = original + np.random.randn(seq_len, channels) * 0.02
    
    # 模拟掩码
    mask = np.random.random((seq_len, channels)) > 0.2  # 20%掩码
    
    print("测试MAE可视化工具...")
    
    # 快速可视化测试
    metrics = quick_mae_visualization(
        original, reconstructed, mask, 
        save_dir="test_visualizations"
    )
    
    print("重建质量指标:")
    print(f"整体MSE: {metrics['overall_mse']:.6f}")
    print(f"通道相关性均值: {np.mean(metrics['correlation_per_channel']):.3f}")