"""
MCG评估指标模块
===============

为MCG三阶段项目提供全面的评估指标体系：

第一阶段 (MAE预训练):
- 重建质量指标: MSE, MAE, PSNR, SSIM
- 表征质量指标: 特征多样性, 空间相关性保持, 时间一致性
- 掩码效果评估: 掩码区域重建质量, 可见区域保持度

后续阶段预留:
- 分类指标: Accuracy, Precision, Recall, F1-Score, AUC
- 回归指标: R², RMSE, Pearson相关系数
- 临床评估指标: 敏感度, 特异度, PPV, NPV
"""

import numpy as np
import torch
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from scipy.stats import pearsonr, spearmanr
from scipy.signal import correlate
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, mean_squared_error, mean_absolute_error, r2_score,
    confusion_matrix
)
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import warnings

import logging
logger = logging.getLogger(__name__)


class MetricsCalculator:
    """
    指标计算器 - 统一的评估指标计算接口
    """
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """重置累积指标"""
        self._accumulated_metrics = {}
        self._sample_count = 0
    
    def update(self, **metrics):
        """更新累积指标"""
        for name, value in metrics.items():
            if name not in self._accumulated_metrics:
                self._accumulated_metrics[name] = []
            self._accumulated_metrics[name].append(value)
        self._sample_count += 1
    
    def compute(self) -> Dict[str, float]:
        """计算最终指标"""
        if self._sample_count == 0:
            return {}
        
        results = {}
        for name, values in self._accumulated_metrics.items():
            if len(values) > 0:
                if isinstance(values[0], (int, float)):
                    results[name] = np.mean(values)
                elif isinstance(values[0], np.ndarray):
                    results[name] = np.mean(values, axis=0)
                else:
                    results[name] = values  # 保留原始值列表
        
        return results


class MAEMetrics:
    """MAE预训练专用指标计算"""
    
    @staticmethod
    def reconstruction_mse(original: torch.Tensor, 
                          reconstructed: torch.Tensor,
                          mask: Optional[torch.Tensor] = None) -> Dict[str, float]:
        """
        计算重建均方误差
        
        Args:
            original: 原始信号 [batch, seq_len, channels]
            reconstructed: 重建信号 [batch, seq_len, channels]
            mask: 掩码 [batch, seq_len, channels], 1=可见, 0=掩码
            
        Returns:
            MSE指标字典
        """
        if mask is not None:
            # 分别计算掩码区域和可见区域的MSE
            masked_region = mask == 0
            visible_region = mask == 1
            
            # 掩码区域MSE (主要关注)
            masked_mse = F.mse_loss(
                original[masked_region], 
                reconstructed[masked_region]
            ).item()
            
            # 可见区域MSE (应该很小)
            visible_mse = F.mse_loss(
                original[visible_region], 
                reconstructed[visible_region]
            ).item()
            
            # 总体MSE
            overall_mse = F.mse_loss(original, reconstructed).item()
            
            return {
                'mse_overall': overall_mse,
                'mse_masked_region': masked_mse,
                'mse_visible_region': visible_mse,
                'mse_ratio_masked_to_visible': masked_mse / (visible_mse + 1e-8)
            }
        else:
            # 无掩码情况
            overall_mse = F.mse_loss(original, reconstructed).item()
            return {'mse_overall': overall_mse}
    
    @staticmethod
    def reconstruction_mae(original: torch.Tensor, 
                          reconstructed: torch.Tensor,
                          mask: Optional[torch.Tensor] = None) -> Dict[str, float]:
        """计算重建平均绝对误差"""
        if mask is not None:
            masked_region = mask == 0
            visible_region = mask == 1
            
            masked_mae = F.l1_loss(
                original[masked_region], 
                reconstructed[masked_region]
            ).item()
            
            visible_mae = F.l1_loss(
                original[visible_region], 
                reconstructed[visible_region]
            ).item()
            
            overall_mae = F.l1_loss(original, reconstructed).item()
            
            return {
                'mae_overall': overall_mae,
                'mae_masked_region': masked_mae,
                'mae_visible_region': visible_mae
            }
        else:
            overall_mae = F.l1_loss(original, reconstructed).item()
            return {'mae_overall': overall_mae}
    
    @staticmethod
    def psnr(original: torch.Tensor, 
             reconstructed: torch.Tensor,
             max_val: Optional[float] = None) -> float:
        """
        计算峰值信噪比 (PSNR)
        
        Args:
            original: 原始信号
            reconstructed: 重建信号
            max_val: 最大信号值，如果None则自动计算
            
        Returns:
            PSNR值 (dB)
        """
        mse = F.mse_loss(original, reconstructed).item()
        if mse == 0:
            return float('inf')
        
        if max_val is None:
            max_val = torch.max(original).item()
        
        psnr_val = 20 * np.log10(max_val) - 10 * np.log10(mse)
        return psnr_val
    
    @staticmethod
    def signal_correlation(original: torch.Tensor, 
                          reconstructed: torch.Tensor,
                          dim: str = 'temporal') -> Dict[str, float]:
        """
        计算信号相关性
        
        Args:
            original: 原始信号 [batch, seq_len, channels]
            reconstructed: 重建信号
            dim: 相关性计算维度 ['temporal', 'spatial', 'both']
            
        Returns:
            相关性指标字典
        """
        original_np = original.detach().cpu().numpy()
        reconstructed_np = reconstructed.detach().cpu().numpy()
        
        results = {}
        
        if dim in ['temporal', 'both']:
            # 时间维度相关性 (每个通道)
            temporal_corrs = []
            for b in range(original_np.shape[0]):
                for ch in range(original_np.shape[2]):
                    corr, _ = pearsonr(original_np[b, :, ch], reconstructed_np[b, :, ch])
                    if not np.isnan(corr):
                        temporal_corrs.append(corr)
            
            results['correlation_temporal_mean'] = np.mean(temporal_corrs)
            results['correlation_temporal_std'] = np.std(temporal_corrs)
        
        if dim in ['spatial', 'both']:
            # 空间维度相关性 (每个时间点)
            spatial_corrs = []
            for b in range(original_np.shape[0]):
                for t in range(original_np.shape[1]):
                    corr, _ = pearsonr(original_np[b, t, :], reconstructed_np[b, t, :])
                    if not np.isnan(corr):
                        spatial_corrs.append(corr)
            
            results['correlation_spatial_mean'] = np.mean(spatial_corrs)
            results['correlation_spatial_std'] = np.std(spatial_corrs)
        
        return results
    
    @staticmethod
    def spectral_similarity(original: torch.Tensor, 
                           reconstructed: torch.Tensor,
                           fs: float = 1000.0) -> Dict[str, float]:
        """
        计算频谱相似性
        
        Args:
            original: 原始信号 [batch, seq_len, channels]
            reconstructed: 重建信号
            fs: 采样频率
            
        Returns:
            频谱相似性指标
        """
        original_np = original.detach().cpu().numpy()
        reconstructed_np = reconstructed.detach().cpu().numpy()
        
        batch_size, seq_len, channels = original_np.shape
        spectral_corrs = []
        spectral_mse_list = []
        
        for b in range(batch_size):
            for ch in range(channels):
                # FFT
                orig_fft = np.fft.fft(original_np[b, :, ch])
                recon_fft = np.fft.fft(reconstructed_np[b, :, ch])
                
                # 功率谱密度
                orig_psd = np.abs(orig_fft) ** 2
                recon_psd = np.abs(recon_fft) ** 2
                
                # 相关性
                corr, _ = pearsonr(orig_psd, recon_psd)
                if not np.isnan(corr):
                    spectral_corrs.append(corr)
                
                # MSE in frequency domain
                spectral_mse = np.mean((orig_psd - recon_psd) ** 2)
                spectral_mse_list.append(spectral_mse)
        
        return {
            'spectral_correlation_mean': np.mean(spectral_corrs),
            'spectral_correlation_std': np.std(spectral_corrs),
            'spectral_mse_mean': np.mean(spectral_mse_list),
            'spectral_mse_std': np.std(spectral_mse_list)
        }


class RepresentationMetrics:
    """表征质量评估指标"""
    
    @staticmethod
    def feature_diversity(features: torch.Tensor) -> float:
        """
        计算特征多样性
        
        Args:
            features: 特征张量 [batch, feature_dim]
            
        Returns:
            多样性分数 (0-1, 越高越好)
        """
        if isinstance(features, torch.Tensor):
            features = features.detach().cpu().numpy()
        
        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # 计算特征的有效维度 (基于PCA)
        pca = PCA()
        pca.fit(features_scaled)
        
        # 计算累积方差解释比例
        cumsum_var_ratio = np.cumsum(pca.explained_variance_ratio_)
        
        # 有效维度：解释90%方差所需的主成分数量
        effective_dims = np.sum(cumsum_var_ratio < 0.9) + 1
        total_dims = features.shape[1]
        
        diversity_score = effective_dims / total_dims
        return float(diversity_score)
    
    @staticmethod
    def representation_collapse_score(features: torch.Tensor, 
                                     threshold: float = 0.99) -> float:
        """
        计算表征崩塌分数
        
        Args:
            features: 特征张量 [batch, feature_dim]
            threshold: 相似性阈值
            
        Returns:
            崩塌分数 (0-1, 越低越好)
        """
        if isinstance(features, torch.Tensor):
            features = features.detach().cpu().numpy()
        
        # 计算样本间的余弦相似度
        features_norm = features / (np.linalg.norm(features, axis=1, keepdims=True) + 1e-8)
        similarity_matrix = np.dot(features_norm, features_norm.T)
        
        # 排除对角线
        mask = ~np.eye(similarity_matrix.shape[0], dtype=bool)
        similarities = similarity_matrix[mask]
        
        # 计算高相似度比例
        high_similarity_ratio = np.mean(similarities > threshold)
        return float(high_similarity_ratio)
    
    @staticmethod
    def spatial_consistency(features: torch.Tensor, 
                           spatial_adjacency: Optional[np.ndarray] = None) -> float:
        """
        计算空间一致性 (相邻通道的特征相似性)
        
        Args:
            features: 通道特征 [channels, feature_dim]
            spatial_adjacency: 空间邻接矩阵 [channels, channels]
            
        Returns:
            空间一致性分数
        """
        if isinstance(features, torch.Tensor):
            features = features.detach().cpu().numpy()
        
        if spatial_adjacency is None:
            # 默认6x6网格的邻接关系
            spatial_adjacency = RepresentationMetrics._create_6x6_adjacency()
        
        # 计算相邻通道特征的相似度
        similarities = []
        for i in range(features.shape[0]):
            for j in range(i + 1, features.shape[0]):
                if spatial_adjacency[i, j] > 0:  # 相邻通道
                    sim = np.corrcoef(features[i], features[j])[0, 1]
                    if not np.isnan(sim):
                        similarities.append(sim)
        
        return float(np.mean(similarities)) if similarities else 0.0
    
    @staticmethod
    def _create_6x6_adjacency() -> np.ndarray:
        """创建6x6网格的邻接矩阵"""
        adjacency = np.zeros((36, 36))
        
        for i in range(6):
            for j in range(6):
                ch_idx = i * 6 + j
                
                # 添加相邻关系 (上下左右)
                directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]
                for di, dj in directions:
                    ni, nj = i + di, j + dj
                    if 0 <= ni < 6 and 0 <= nj < 6:
                        neighbor_idx = ni * 6 + nj
                        adjacency[ch_idx, neighbor_idx] = 1
        
        return adjacency
    
    @staticmethod
    def temporal_consistency(features: torch.Tensor, 
                            window_size: int = 50) -> float:
        """
        计算时间一致性 (相邻时间窗口的特征相似性)
        
        Args:
            features: 时间特征 [seq_len, feature_dim]
            window_size: 时间窗口大小
            
        Returns:
            时间一致性分数
        """
        if isinstance(features, torch.Tensor):
            features = features.detach().cpu().numpy()
        
        seq_len = features.shape[0]
        if seq_len < 2 * window_size:
            return 0.0
        
        similarities = []
        
        for t in range(0, seq_len - 2 * window_size, window_size):
            # 当前窗口和下一个窗口
            window1 = features[t:t + window_size]
            window2 = features[t + window_size:t + 2 * window_size]
            
            # 计算窗口内的平均特征
            mean1 = np.mean(window1, axis=0)
            mean2 = np.mean(window2, axis=0)
            
            # 相关性
            corr = np.corrcoef(mean1, mean2)[0, 1]
            if not np.isnan(corr):
                similarities.append(corr)
        
        return float(np.mean(similarities)) if similarities else 0.0


class ClinicalMetrics:
    """临床评估指标 (预留给后续阶段)"""
    
    @staticmethod
    def binary_classification_metrics(y_true: np.ndarray, 
                                     y_pred: np.ndarray,
                                     y_prob: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        二分类指标计算
        
        Args:
            y_true: 真实标签 [n_samples]
            y_pred: 预测标签 [n_samples]
            y_prob: 预测概率 [n_samples] (可选)
            
        Returns:
            二分类指标字典
        """
        metrics = {}
        
        # 基础指标
        metrics['accuracy'] = accuracy_score(y_true, y_pred)
        metrics['precision'] = precision_score(y_true, y_pred, zero_division=0)
        metrics['recall'] = recall_score(y_true, y_pred, zero_division=0)  # 敏感度
        metrics['f1_score'] = f1_score(y_true, y_pred, zero_division=0)
        
        # 混淆矩阵指标
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
        
        metrics['sensitivity'] = tp / (tp + fn) if (tp + fn) > 0 else 0  # 敏感度/召回率
        metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0  # 特异度
        metrics['ppv'] = tp / (tp + fp) if (tp + fp) > 0 else 0  # 阳性预测值
        metrics['npv'] = tn / (tn + fn) if (tn + fn) > 0 else 0  # 阴性预测值
        
        # AUC (如果有概率)
        if y_prob is not None:
            try:
                metrics['auc'] = roc_auc_score(y_true, y_prob)
            except ValueError:
                metrics['auc'] = 0.0
        
        return metrics
    
    @staticmethod
    def regression_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """
        回归指标计算 (用于血管狭窄程度预测)
        
        Args:
            y_true: 真实值 [n_samples] 或 [n_samples, n_outputs]
            y_pred: 预测值 [n_samples] 或 [n_samples, n_outputs]
            
        Returns:
            回归指标字典
        """
        metrics = {}
        
        # 基础回归指标
        metrics['mse'] = mean_squared_error(y_true, y_pred)
        metrics['mae'] = mean_absolute_error(y_true, y_pred)
        metrics['rmse'] = np.sqrt(metrics['mse'])
        
        try:
            metrics['r2'] = r2_score(y_true, y_pred)
        except ValueError:
            metrics['r2'] = -np.inf
        
        # Pearson相关系数
        if y_true.ndim == 1:
            corr, _ = pearsonr(y_true, y_pred)
            metrics['pearson_r'] = corr if not np.isnan(corr) else 0.0
        else:
            # 多输出情况
            correlations = []
            for i in range(y_true.shape[1]):
                corr, _ = pearsonr(y_true[:, i], y_pred[:, i])
                if not np.isnan(corr):
                    correlations.append(corr)
            metrics['pearson_r_mean'] = np.mean(correlations) if correlations else 0.0
        
        return metrics
    
    @staticmethod
    def multi_task_metrics(vessel_true: np.ndarray, vessel_pred: np.ndarray,
                          diagnosis_true: np.ndarray, diagnosis_pred: np.ndarray,
                          diagnosis_prob: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        多任务学习指标 (血管狭窄 + 诊断分类)
        
        Args:
            vessel_true: 血管狭窄真实值 [n_samples, 4]
            vessel_pred: 血管狭窄预测值 [n_samples, 4]
            diagnosis_true: 诊断真实标签 [n_samples]
            diagnosis_pred: 诊断预测标签 [n_samples]
            diagnosis_prob: 诊断预测概率 [n_samples] (可选)
            
        Returns:
            综合指标字典
        """
        metrics = {}
        
        # 血管狭窄回归指标
        vessel_metrics = ClinicalMetrics.regression_metrics(vessel_true, vessel_pred)
        for k, v in vessel_metrics.items():
            metrics[f'vessel_{k}'] = v
        
        # 诊断分类指标
        diagnosis_metrics = ClinicalMetrics.binary_classification_metrics(
            diagnosis_true, diagnosis_pred, diagnosis_prob
        )
        for k, v in diagnosis_metrics.items():
            metrics[f'diagnosis_{k}'] = v
        
        # 综合指标
        metrics['combined_score'] = (
            vessel_metrics.get('pearson_r_mean', 0) * 0.5 + 
            diagnosis_metrics.get('auc', diagnosis_metrics.get('f1_score', 0)) * 0.5
        )
        
        return metrics


class MetricsAggregator:
    """指标聚合器 - 用于实验追踪和报告"""
    
    def __init__(self):
        self.mae_calculator = MetricsCalculator()
        self.representation_calculator = MetricsCalculator()
        self.clinical_calculator = MetricsCalculator()
        
        self.epoch_metrics = {}
    
    def update_mae_metrics(self, original: torch.Tensor, 
                          reconstructed: torch.Tensor,
                          mask: Optional[torch.Tensor] = None):
        """更新MAE指标"""
        # 重建质量指标
        mse_metrics = MAEMetrics.reconstruction_mse(original, reconstructed, mask)
        mae_metrics = MAEMetrics.reconstruction_mae(original, reconstructed, mask)
        
        # PSNR
        psnr_val = MAEMetrics.psnr(original, reconstructed)
        
        # 相关性
        corr_metrics = MAEMetrics.signal_correlation(original, reconstructed, 'both')
        
        # 频谱相似性
        spectral_metrics = MAEMetrics.spectral_similarity(original, reconstructed)
        
        # 合并所有指标
        all_metrics = {
            **mse_metrics,
            **mae_metrics,
            'psnr': psnr_val,
            **corr_metrics,
            **spectral_metrics
        }
        
        self.mae_calculator.update(**all_metrics)
    
    def update_representation_metrics(self, features: torch.Tensor):
        """更新表征质量指标"""
        diversity = RepresentationMetrics.feature_diversity(features)
        collapse = RepresentationMetrics.representation_collapse_score(features)
        
        self.representation_calculator.update(
            feature_diversity=diversity,
            representation_collapse=collapse
        )
    
    def update_clinical_metrics(self, **kwargs):
        """更新临床指标 (预留)"""
        self.clinical_calculator.update(**kwargs)
    
    def compute_epoch_metrics(self, epoch: int) -> Dict[str, float]:
        """计算并保存epoch指标"""
        mae_metrics = self.mae_calculator.compute()
        repr_metrics = self.representation_calculator.compute()
        clinical_metrics = self.clinical_calculator.compute()
        
        epoch_result = {
            **{f'mae_{k}': v for k, v in mae_metrics.items()},
            **{f'repr_{k}': v for k, v in repr_metrics.items()},
            **{f'clinical_{k}': v for k, v in clinical_metrics.items()}
        }
        
        self.epoch_metrics[epoch] = epoch_result
        
        # 重置calculator准备下个epoch
        self.mae_calculator.reset()
        self.representation_calculator.reset()
        self.clinical_calculator.reset()
        
        return epoch_result
    
    def get_best_metrics(self, key: str = 'mae_mse_overall', 
                        mode: str = 'min') -> Tuple[int, Dict[str, float]]:
        """
        获取最佳性能的epoch和指标
        
        Args:
            key: 用于比较的指标键
            mode: 'min' 或 'max'
            
        Returns:
            (best_epoch, best_metrics)
        """
        if not self.epoch_metrics:
            return -1, {}
        
        if mode == 'min':
            best_epoch = min(self.epoch_metrics.keys(), 
                           key=lambda e: self.epoch_metrics[e].get(key, float('inf')))
        else:
            best_epoch = max(self.epoch_metrics.keys(),
                           key=lambda e: self.epoch_metrics[e].get(key, -float('inf')))
        
        return best_epoch, self.epoch_metrics[best_epoch]
    
    def export_metrics_df(self) -> 'pd.DataFrame':
        """导出指标为pandas DataFrame"""
        import pandas as pd
        
        if not self.epoch_metrics:
            return pd.DataFrame()
        
        df = pd.DataFrame.from_dict(self.epoch_metrics, orient='index')
        df.index.name = 'epoch'
        return df


# 便利函数
def compute_mae_metrics(original: torch.Tensor, 
                       reconstructed: torch.Tensor,
                       mask: Optional[torch.Tensor] = None,
                       include_spectral: bool = True) -> Dict[str, float]:
    """一键计算所有MAE相关指标"""
    metrics = {}
    
    # 重建质量
    metrics.update(MAEMetrics.reconstruction_mse(original, reconstructed, mask))
    metrics.update(MAEMetrics.reconstruction_mae(original, reconstructed, mask))
    metrics['psnr'] = MAEMetrics.psnr(original, reconstructed)
    
    # 信号相关性
    metrics.update(MAEMetrics.signal_correlation(original, reconstructed, 'both'))
    
    # 频谱分析 (可选，计算较慢)
    if include_spectral:
        metrics.update(MAEMetrics.spectral_similarity(original, reconstructed))
    
    return metrics


if __name__ == "__main__":
    # 测试代码
    batch_size, seq_len, channels = 8, 1500, 36
    
    # 生成测试数据
    original = torch.randn(batch_size, seq_len, channels)
    reconstructed = original + torch.randn_like(original) * 0.1
    mask = torch.rand(batch_size, seq_len, channels) > 0.25  # 25%掩码
    
    print("测试MAE指标计算...")
    
    # 计算指标
    mae_metrics = compute_mae_metrics(original, reconstructed, mask)
    
    print("MAE指标结果:")
    for name, value in mae_metrics.items():
        if isinstance(value, float):
            print(f"{name}: {value:.6f}")
        else:
            print(f"{name}: {value}")
    
    # 测试表征指标
    features = torch.randn(batch_size, 256)  # 假设特征维度256
    
    diversity = RepresentationMetrics.feature_diversity(features)
    collapse = RepresentationMetrics.representation_collapse_score(features)
    
    print(f"\\n表征质量指标:")
    print(f"特征多样性: {diversity:.4f}")
    print(f"表征崩塌分数: {collapse:.4f}")
    
    # 测试指标聚合器
    print("\\n测试指标聚合器...")
    aggregator = MetricsAggregator()
    
    for epoch in range(3):
        # 模拟一个epoch的指标更新
        aggregator.update_mae_metrics(original, reconstructed, mask)
        aggregator.update_representation_metrics(features)
        
        epoch_metrics = aggregator.compute_epoch_metrics(epoch)
        print(f"Epoch {epoch} 指标数量: {len(epoch_metrics)}")
    
    # 获取最佳结果
    best_epoch, best_metrics = aggregator.get_best_metrics('mae_mse_overall', 'min')
    print(f"最佳epoch: {best_epoch}, MSE: {best_metrics.get('mae_mse_overall', 0):.6f}")
    
    print("指标计算测试完成！")