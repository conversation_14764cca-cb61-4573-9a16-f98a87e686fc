"""
MCG数据增强模块
===============

为MCG信号提供专门的数据增强策略，特别针对MAE预训练优化：
1. 生理学相关的噪声模拟
2. 时空域的变换增强
3. 对比学习增强策略
4. 渐进式噪声调度
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from scipy import signal
from scipy.interpolate import interp1d
import random
import warnings

import logging
logger = logging.getLogger(__name__)


class MCGAugmentationRegistry:
    """增强策略注册器 - 便于扩展和管理"""
    
    _strategies = {}
    
    @classmethod
    def register(cls, name: str):
        """注册增强策略装饰器"""
        def decorator(func):
            cls._strategies[name] = func
            return func
        return decorator
    
    @classmethod
    def get_strategy(cls, name: str) -> Callable:
        """获取增强策略"""
        if name not in cls._strategies:
            raise ValueError(f"未知的增强策略: {name}")
        return cls._strategies[name]
    
    @classmethod
    def list_strategies(cls) -> List[str]:
        """列出所有可用策略"""
        return list(cls._strategies.keys())


class MCGAugmentor:
    """MCG数据增强器 - 为MAE预训练优化"""
    
    def __init__(self, config: Dict[str, Any], training_phase: str = 'pretrain'):
        """
        初始化增强器
        
        Args:
            config: 增强配置字典
            training_phase: 训练阶段 ['pretrain', 'finetune', 'inference']
        """
        self.config = config
        self.training_phase = training_phase
        self.enabled_strategies = []
        
        # 解析配置，构建增强管道
        self._build_augmentation_pipeline()
        
        # MAE特定的噪声调度
        self.noise_scheduler = NoiseScheduler(config) if training_phase == 'pretrain' else None
        
        logger.info(f"增强器初始化完成: {training_phase}, "
                   f"策略数={len(self.enabled_strategies)}")
    
    def _build_augmentation_pipeline(self):
        """构建增强管道"""
        for strategy_name, strategy_config in self.config.items():
            if strategy_config.get('enabled', False):
                strategy_func = MCGAugmentationRegistry.get_strategy(strategy_name)
                self.enabled_strategies.append({
                    'name': strategy_name,
                    'func': strategy_func,
                    'config': strategy_config,
                    'probability': strategy_config.get('probability', 1.0)
                })
    
    def __call__(self, signal: np.ndarray, 
                 epoch: Optional[int] = None,
                 **kwargs) -> np.ndarray:
        """
        应用数据增强
        
        Args:
            signal: 输入信号 [seq_len, channels]
            epoch: 当前训练轮数（用于噪声调度）
            
        Returns:
            增强后的信号
        """
        if self.training_phase == 'inference':
            return signal  # 推理阶段不应用增强
        
        augmented = signal.copy()
        
        # 应用启用的增强策略
        for strategy in self.enabled_strategies:
            if random.random() < strategy['probability']:
                try:
                    # 调整噪声强度（如果有调度器）
                    strategy_config = strategy['config'].copy()
                    if (self.noise_scheduler is not None and 
                        'noise' in strategy['name'].lower() and 
                        epoch is not None):
                        strategy_config = self.noise_scheduler.adjust_config(
                            strategy_config, epoch
                        )
                    
                    augmented = strategy['func'](augmented, strategy_config)
                except Exception as e:
                    logger.warning(f"增强策略失败 {strategy['name']}: {e}")
        
        return augmented
    
    def set_training_phase(self, phase: str):
        """设置训练阶段"""
        self.training_phase = phase
        logger.info(f"增强器阶段切换到: {phase}")


class NoiseScheduler:
    """噪声调度器 - 为MAE预训练提供渐进式噪声"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.scheduler_config = config.get('noise_scheduling', {})
        
    def adjust_config(self, strategy_config: Dict[str, Any], 
                     epoch: int) -> Dict[str, Any]:
        """
        根据训练进度调整噪声配置
        
        Args:
            strategy_config: 策略配置
            epoch: 当前轮数
            
        Returns:
            调整后的配置
        """
        if not self.scheduler_config.get('enabled', False):
            return strategy_config
        
        total_epochs = self.scheduler_config.get('total_epochs', 100)
        warmup_epochs = self.scheduler_config.get('warmup_epochs', 20)
        
        # 计算噪声强度调整因子
        if epoch < warmup_epochs:
            # 预热阶段：从低噪声开始
            factor = 0.5 + 0.5 * (epoch / warmup_epochs)
        else:
            # 训练阶段：逐渐增加噪声
            progress = (epoch - warmup_epochs) / (total_epochs - warmup_epochs)
            factor = 0.5 + 0.5 * (1 - progress)  # 后期适当降低噪声
        
        # 调整噪声强度相关参数
        adjusted_config = strategy_config.copy()
        for key in ['std_range', 'amplitude_range', 'scale_range']:
            if key in adjusted_config:
                original_range = adjusted_config[key]
                adjusted_config[key] = [
                    original_range[0] * factor,
                    original_range[1] * factor
                ]
        
        return adjusted_config


# ============= 增强策略实现 =============

@MCGAugmentationRegistry.register('gaussian_noise')
def add_gaussian_noise(signal: np.ndarray, config: Dict) -> np.ndarray:
    """
    添加高斯噪声
    
    Args:
        signal: 输入信号 [seq_len, channels]
        config: 噪声配置
        
    Returns:
        加噪后的信号
    """
    std_range = config.get('std_range', [0.01, 0.05])
    noise_std = np.random.uniform(std_range[0], std_range[1])
    
    noise = np.random.normal(0, noise_std, signal.shape)
    return signal + noise.astype(signal.dtype)


@MCGAugmentationRegistry.register('powerline_noise')
def add_powerline_noise(signal: np.ndarray, config: Dict) -> np.ndarray:
    """
    添加工频干扰噪声（50/60Hz）
    
    Args:
        signal: 输入信号 [seq_len, channels]
        config: 工频噪声配置
        
    Returns:
        加噪后的信号
    """
    frequencies = config.get('frequency', [50, 60])
    amplitude_range = config.get('amplitude_range', [0.01, 0.03])
    sampling_rate = config.get('sampling_rate', 1000)  # 假设采样率1000Hz
    
    freq = np.random.choice(frequencies)
    amplitude = np.random.uniform(amplitude_range[0], amplitude_range[1])
    
    seq_len = signal.shape[0]
    t = np.linspace(0, seq_len / sampling_rate, seq_len)
    
    # 同步添加到所有通道（模拟环境干扰）
    powerline_noise = amplitude * np.sin(2 * np.pi * freq * t)
    noise_matrix = powerline_noise[:, np.newaxis].repeat(signal.shape[1], axis=1)
    
    return signal + noise_matrix.astype(signal.dtype)


@MCGAugmentationRegistry.register('baseline_drift')
def add_baseline_drift(signal: np.ndarray, config: Dict) -> np.ndarray:
    """
    添加基线漂移
    
    Args:
        signal: 输入信号
        config: 基线漂移配置
        
    Returns:
        添加基线漂移后的信号
    """
    drift_type = config.get('drift_type', 'linear')  # ['linear', 'sinusoidal', 'exponential']
    amplitude_range = config.get('amplitude_range', [0.01, 0.05])
    
    seq_len = signal.shape[0]
    amplitude = np.random.uniform(amplitude_range[0], amplitude_range[1])
    
    if drift_type == 'linear':
        drift = amplitude * np.linspace(-1, 1, seq_len)
    elif drift_type == 'sinusoidal':
        freq = config.get('drift_frequency', 0.1)  # 低频漂移
        drift = amplitude * np.sin(2 * np.pi * freq * np.linspace(0, 1, seq_len))
    elif drift_type == 'exponential':
        decay = config.get('decay_rate', 0.01)
        drift = amplitude * (np.exp(-decay * np.arange(seq_len)) - 0.5)
    else:
        raise ValueError(f"不支持的漂移类型: {drift_type}")
    
    # 应用到所有通道
    drift_matrix = drift[:, np.newaxis].repeat(signal.shape[1], axis=1)
    return signal + drift_matrix.astype(signal.dtype)


@MCGAugmentationRegistry.register('channel_dropout')
def channel_dropout(signal: np.ndarray, config: Dict) -> np.ndarray:
    """
    通道dropout - 随机将某些通道设为零
    
    Args:
        signal: 输入信号
        config: dropout配置
        
    Returns:
        dropout后的信号
    """
    dropout_prob = config.get('dropout_prob', 0.1)
    num_channels = signal.shape[1]
    
    # 随机选择要dropout的通道
    dropout_mask = np.random.random(num_channels) < dropout_prob
    
    augmented = signal.copy()
    augmented[:, dropout_mask] = 0
    
    return augmented


@MCGAugmentationRegistry.register('time_shift')
def time_shift(signal: np.ndarray, config: Dict) -> np.ndarray:
    """
    时间偏移
    
    Args:
        signal: 输入信号
        config: 时间偏移配置
        
    Returns:
        时间偏移后的信号
    """
    shift_range = config.get('shift_range', [-50, 50])
    shift = np.random.randint(shift_range[0], shift_range[1])
    
    if shift == 0:
        return signal
    
    augmented = signal.copy()
    if shift > 0:
        # 右移：前面补零，后面截断
        augmented[shift:] = signal[:-shift]
        augmented[:shift] = 0
    else:
        # 左移：后面补零，前面截断
        augmented[:shift] = signal[-shift:]
        augmented[shift:] = 0
    
    return augmented


@MCGAugmentationRegistry.register('time_stretch')
def time_stretch(signal: np.ndarray, config: Dict) -> np.ndarray:
    """
    时间拉伸/压缩
    
    Args:
        signal: 输入信号
        config: 时间拉伸配置
        
    Returns:
        时间拉伸后的信号
    """
    stretch_range = config.get('stretch_range', [0.9, 1.1])
    stretch_factor = np.random.uniform(stretch_range[0], stretch_range[1])
    
    seq_len = signal.shape[0]
    new_len = int(seq_len * stretch_factor)
    
    # 使用插值进行拉伸
    old_indices = np.arange(seq_len)
    new_indices = np.linspace(0, seq_len - 1, new_len)
    
    augmented = np.zeros((new_len, signal.shape[1]), dtype=signal.dtype)
    
    for ch in range(signal.shape[1]):
        interp_func = interp1d(old_indices, signal[:, ch], 
                              kind='linear', bounds_error=False, fill_value=0)
        augmented[:, ch] = interp_func(new_indices)
    
    # 如果拉伸后长度不匹配，需要裁剪或填充到原长度
    if new_len > seq_len:
        return augmented[:seq_len]
    else:
        padded = np.zeros_like(signal)
        padded[:new_len] = augmented
        return padded


@MCGAugmentationRegistry.register('amplitude_scaling')
def amplitude_scaling(signal: np.ndarray, config: Dict) -> np.ndarray:
    """
    幅值缩放
    
    Args:
        signal: 输入信号
        config: 幅值缩放配置
        
    Returns:
        幅值缩放后的信号
    """
    scale_range = config.get('scale_range', [0.8, 1.2])
    scale_type = config.get('scale_type', 'global')  # ['global', 'channel_wise']
    
    if scale_type == 'global':
        # 全局缩放
        scale_factor = np.random.uniform(scale_range[0], scale_range[1])
        return signal * scale_factor
    
    elif scale_type == 'channel_wise':
        # 通道级缩放
        num_channels = signal.shape[1]
        scale_factors = np.random.uniform(scale_range[0], scale_range[1], num_channels)
        return signal * scale_factors[np.newaxis, :]
    
    else:
        raise ValueError(f"不支持的缩放类型: {scale_type}")


@MCGAugmentationRegistry.register('frequency_masking')
def frequency_masking(signal: np.ndarray, config: Dict) -> np.ndarray:
    """
    频域掩码 - 在频域中掩盖某些频率分量
    
    Args:
        signal: 输入信号
        config: 频域掩码配置
        
    Returns:
        频域掩码后的信号
    """
    mask_prob = config.get('mask_prob', 0.1)
    freq_range = config.get('freq_range', [1, 100])  # Hz
    sampling_rate = config.get('sampling_rate', 1000)
    
    augmented = signal.copy()
    
    for ch in range(signal.shape[1]):
        channel_signal = signal[:, ch]
        
        # FFT
        fft_signal = np.fft.fft(channel_signal)
        freqs = np.fft.fftfreq(len(channel_signal), 1/sampling_rate)
        
        # 创建掩码
        mask_indices = ((np.abs(freqs) >= freq_range[0]) & 
                       (np.abs(freqs) <= freq_range[1]) &
                       (np.random.random(len(freqs)) < mask_prob))
        
        # 应用掩码
        fft_signal[mask_indices] = 0
        
        # IFFT
        augmented[:, ch] = np.real(np.fft.ifft(fft_signal))
    
    return augmented.astype(signal.dtype)


@MCGAugmentationRegistry.register('spatial_mixing')
def spatial_mixing(signal: np.ndarray, config: Dict) -> np.ndarray:
    """
    空间混合 - 通道间的线性组合
    
    Args:
        signal: 输入信号 [seq_len, 36]
        config: 空间混合配置
        
    Returns:
        空间混合后的信号
    """
    mix_prob = config.get('mix_prob', 0.3)
    mix_strength = config.get('mix_strength', 0.1)
    
    if np.random.random() > mix_prob:
        return signal
    
    num_channels = signal.shape[1]
    augmented = signal.copy()
    
    # 随机选择要混合的通道对
    ch1, ch2 = np.random.choice(num_channels, size=2, replace=False)
    
    # 线性混合
    alpha = np.random.uniform(0, mix_strength)
    augmented[:, ch1] = (1 - alpha) * signal[:, ch1] + alpha * signal[:, ch2]
    augmented[:, ch2] = (1 - alpha) * signal[:, ch2] + alpha * signal[:, ch1]
    
    return augmented


# 注意：ContrastiveAugmentor已移除
# MAE预训练不需要对比学习增强，重建任务本身提供足够的监督信号
# 如果后续需要对比学习方法，可以单独实现专用的对比学习模块


# 便利函数
def create_mae_augmentor(config: Dict[str, Any]) -> MCGAugmentor:
    """创建MAE预训练专用的增强器"""
    mae_config = {
        'gaussian_noise': {
            'enabled': True,
            'std_range': config['augmentation']['gaussian_noise']['std_range'],
            'probability': config['augmentation']['gaussian_noise']['probability']
        }
    }
    
    # 添加其他启用的增强策略
    for strategy_name, strategy_config in config['augmentation'].items():
        if strategy_name != 'gaussian_noise' and strategy_config.get('enabled', False):
            mae_config[strategy_name] = strategy_config
    
    return MCGAugmentor(mae_config, training_phase='pretrain')


if __name__ == "__main__":
    # 测试增强策略
    import matplotlib.pyplot as plt
    
    # 创建测试信号
    seq_len, channels = 1500, 36
    test_signal = np.random.randn(seq_len, channels) * 0.1
    
    # 测试配置
    test_config = {
        'gaussian_noise': {
            'enabled': True,
            'std_range': [0.01, 0.05],
            'probability': 1.0
        },
        'powerline_noise': {
            'enabled': True,
            'frequency': [50],
            'amplitude_range': [0.02, 0.02],
            'probability': 1.0,
            'sampling_rate': 1000
        },
        'baseline_drift': {
            'enabled': True,
            'drift_type': 'linear',
            'amplitude_range': [0.03, 0.03],
            'probability': 1.0
        }
    }
    
    # 创建增强器
    augmentor = MCGAugmentor(test_config)
    
    # 应用增强
    augmented_signal = augmentor(test_signal)
    
    print(f"原始信号形状: {test_signal.shape}")
    print(f"增强信号形状: {augmented_signal.shape}")
    print(f"信号变化: {np.mean(np.abs(augmented_signal - test_signal)):.6f}")
    
    # 可视化（选择一个通道）
    plt.figure(figsize=(12, 4))
    plt.subplot(1, 2, 1)
    plt.plot(test_signal[:500, 0])
    plt.title('原始信号')
    plt.subplot(1, 2, 2)
    plt.plot(augmented_signal[:500, 0])
    plt.title('增强信号')
    plt.tight_layout()
    plt.savefig('augmentation_test.png')
    plt.close()
    
    print("增强策略测试完成，结果保存到 augmentation_test.png")
    
    print("注意：对比学习增强器已移除，MAE预训练使用重建任务作为监督信号")