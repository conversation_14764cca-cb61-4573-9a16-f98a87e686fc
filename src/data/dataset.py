"""
MCG数据集模块
============

为MCG三阶段项目提供统一的数据加载接口，支持：
1. 第一阶段MAE预训练的无标签数据加载
2. 后续阶段的标签数据加载（预留接口）
3. 动态数据增强和预处理
4. 内存效率优化和缓存机制
"""

import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
import pickle
from functools import lru_cache
import warnings

from .preprocessing import MCGPreprocessor, MCGAugmentor

logger = logging.getLogger(__name__)


class BaseMCGDataset(Dataset):
    """MCG数据集基类 - 可继承扩展用于不同阶段"""
    
    def __init__(self, 
                 data_root: Union[str, Path],
                 config: Dict[str, Any],
                 split: str = 'train',
                 apply_augmentation: bool = False,
                 cache_processed: bool = True,
                 max_cache_size: int = 1000):
        """
        初始化数据集
        
        Args:
            data_root: MCG数据根目录
            config: 配置字典（来自preprocessing.yaml）
            split: 数据分割 ['train', 'val', 'test']
            apply_augmentation: 是否应用数据增强
            cache_processed: 是否缓存预处理结果
            max_cache_size: 最大缓存样本数
        """
        self.data_root = Path(data_root)
        self.config = config
        self.split = split
        self.apply_augmentation = apply_augmentation
        self.cache_processed = cache_processed
        
        # 初始化预处理器和增强器
        self.preprocessor = MCGPreprocessor(config)
        self.augmentor = MCGAugmentor(config) if apply_augmentation else None
        
        # 缓存系统
        self.cache = {} if cache_processed else None
        self.max_cache_size = max_cache_size
        
        # 加载样本列表
        self.sample_list = self._discover_samples()
        self.sample_ids = [s['id'] for s in self.sample_list]
        
        logger.info(f"数据集初始化完成: {split}, 样本数={len(self.sample_list)}")
        
        # 统计信息
        self.stats = {
            'total_samples': len(self.sample_list),
            'cache_hits': 0,
            'cache_misses': 0,
            'preprocessing_errors': 0
        }
    
    def _discover_samples(self) -> List[Dict[str, Any]]:
        """
        发现并整理所有MCG样本文件
        
        Returns:
            样本信息列表
        """
        sample_list = []
        
        # 查找所有.txt文件
        txt_files = list(self.data_root.glob("*.txt"))
        logger.info(f"发现{len(txt_files)}个MCG文件")
        
        for txt_file in txt_files:
            sample_info = {
                'id': txt_file.stem,
                'file_path': txt_file,
                'file_size': txt_file.stat().st_size
            }
            sample_list.append(sample_info)
        
        # 根据配置进行数据分割（对于MAE预训练，通常使用全部数据）
        split_config = self.config['data_split']
        if split_config['train_ratio'] == 1.0:
            # 使用全部数据（第一阶段MAE）
            return sample_list
        else:
            # 按比例分割数据（后续阶段）
            return self._split_samples(sample_list, split_config)
    
    def _split_samples(self, sample_list: List[Dict], 
                      split_config: Dict) -> List[Dict]:
        """
        按配置分割数据（预留给后续阶段使用）
        """
        total = len(sample_list)
        train_size = int(total * split_config['train_ratio'])
        val_size = int(total * split_config['val_ratio'])
        
        # 设置随机种子确保可复现
        np.random.seed(split_config['random_seed'])
        indices = np.random.permutation(total)
        
        if self.split == 'train':
            selected_indices = indices[:train_size]
        elif self.split == 'val':
            selected_indices = indices[train_size:train_size + val_size]
        elif self.split == 'test':
            selected_indices = indices[train_size + val_size:]
        else:
            raise ValueError(f"不支持的split类型: {self.split}")
        
        return [sample_list[i] for i in selected_indices]
    
    def __len__(self) -> int:
        return len(self.sample_list)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        获取单个样本
        
        Args:
            idx: 样本索引
            
        Returns:
            包含信号数据的字典
        """
        sample_info = self.sample_list[idx]
        sample_id = sample_info['id']
        
        # 检查缓存
        if self.cache is not None and sample_id in self.cache:
            self.stats['cache_hits'] += 1
            signal = self.cache[sample_id].copy()  # 防止修改缓存数据
        else:
            # 从文件加载和预处理
            self.stats['cache_misses'] += 1
            signal = self._load_and_preprocess(sample_info)
            
            # 加入缓存
            if (self.cache is not None and 
                len(self.cache) < self.max_cache_size and 
                signal is not None):
                self.cache[sample_id] = signal.copy()
        
        if signal is None:
            self.stats['preprocessing_errors'] += 1
            # 返回一个空的tensor，由DataLoader的collate_fn处理
            return self._get_empty_sample()
        
        # 应用数据增强（训练时）
        if self.augmentor is not None and self.split == 'train':
            signal = self.augmentor.apply_augmentation(signal)
        
        return self._format_sample(signal, sample_info)
    
    def _load_and_preprocess(self, sample_info: Dict) -> Optional[np.ndarray]:
        """加载并预处理单个样本"""
        try:
            signal = self.preprocessor.process_single(
                sample_info['file_path'], 
                sample_info['id']
            )
            return signal
        except Exception as e:
            logger.error(f"预处理失败 {sample_info['id']}: {e}")
            return None
    
    def _format_sample(self, signal: np.ndarray, 
                      sample_info: Dict) -> Dict[str, torch.Tensor]:
        """格式化样本数据为PyTorch tensor"""
        return {
            'signal': torch.from_numpy(signal).float(),  # [seq_len, channels]
            'sample_id': sample_info['id'],
            'file_path': str(sample_info['file_path'])
        }
    
    def _get_empty_sample(self) -> Dict[str, torch.Tensor]:
        """获取空样本（用于错误处理）"""
        target_length = self.config['data_config']['target_length']
        channels = self.config['data_config']['channels']
        
        return {
            'signal': torch.zeros(target_length, channels, dtype=torch.float),
            'sample_id': 'empty',
            'file_path': ''
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        cache_hit_rate = (self.stats['cache_hits'] / 
                         (self.stats['cache_hits'] + self.stats['cache_misses'])
                         if (self.stats['cache_hits'] + self.stats['cache_misses']) > 0 else 0)
        
        return {
            **self.stats,
            'cache_hit_rate': cache_hit_rate,
            'cache_size': len(self.cache) if self.cache else 0,
            'quality_report': self.preprocessor.get_quality_report()
        }
    
    def save_cache(self, cache_path: Union[str, Path]):
        """保存缓存到磁盘"""
        if self.cache:
            cache_path = Path(cache_path)
            cache_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(cache_path, 'wb') as f:
                pickle.dump(self.cache, f)
            logger.info(f"缓存已保存到: {cache_path}")
    
    def load_cache(self, cache_path: Union[str, Path]):
        """从磁盘加载缓存"""
        cache_path = Path(cache_path)
        if cache_path.exists():
            with open(cache_path, 'rb') as f:
                self.cache = pickle.load(f)
            logger.info(f"缓存已加载: {len(self.cache)}个样本")


class MAEPretrainDataset(BaseMCGDataset):
    """MAE预训练专用数据集"""
    
    def __init__(self, data_root: Union[str, Path], config: Dict[str, Any], 
                 apply_augmentation: bool = True, **kwargs):
        """
        MAE预训练数据集
        
        Args:
            data_root: 数据根目录
            config: 配置字典
            apply_augmentation: 是否应用数据增强
        """
        super().__init__(
            data_root=data_root,
            config=config,
            split='train',  # MAE预训练通常使用全部数据
            apply_augmentation=apply_augmentation,
            **kwargs
        )
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        获取MAE训练样本
        
        Returns:
            包含原始信号和增强信号的字典（用于对比学习）
        """
        sample = super().__getitem__(idx)
        
        if sample['sample_id'] == 'empty':
            return sample
        
        # 为MAE准备数据：原始信号作为重建目标
        result = {
            'signal': sample['signal'],  # 作为输入
            'target': sample['signal'].clone(),  # 作为重建目标
            'sample_id': sample['sample_id'],
            'file_path': sample['file_path']
        }
        
        # 如果启用了噪声增强，输入信号已经被增强，目标保持干净
        # 这符合"加噪输入-重建干净信号"的策略
        if self.augmentor is not None:
            # 获取干净目标（不应用增强）
            clean_signal = self._load_and_preprocess(self.sample_list[idx])
            if clean_signal is not None:
                result['target'] = torch.from_numpy(clean_signal).float()
        
        return result


def collate_fn_mae(batch: List[Dict]) -> Dict[str, torch.Tensor]:
    """
    MAE训练的批处理函数
    
    Args:
        batch: 批数据列表
        
    Returns:
        批处理后的数据字典
    """
    # 过滤空样本
    valid_batch = [item for item in batch if item['sample_id'] != 'empty']
    
    if not valid_batch:
        # 所有样本都无效，返回空批次
        return {
            'signal': torch.empty(0),
            'target': torch.empty(0),
            'sample_ids': [],
            'file_paths': []
        }
    
    # 堆叠张量
    signals = torch.stack([item['signal'] for item in valid_batch])
    targets = torch.stack([item['target'] for item in valid_batch])
    sample_ids = [item['sample_id'] for item in valid_batch]
    file_paths = [item['file_path'] for item in valid_batch]
    
    return {
        'signal': signals,      # [batch, seq_len, channels]
        'target': targets,      # [batch, seq_len, channels]
        'sample_ids': sample_ids,
        'file_paths': file_paths
    }


def create_mae_dataloader(data_root: Union[str, Path],
                         config: Dict[str, Any],
                         batch_size: Optional[int] = None,
                         num_workers: Optional[int] = None,
                         **kwargs) -> DataLoader:
    """
    创建MAE预训练数据加载器
    
    Args:
        data_root: 数据根目录
        config: 配置字典
        batch_size: 批大小（从配置读取如果未指定）
        num_workers: 工作进程数
        
    Returns:
        MAE数据加载器
    """
    # 从配置获取参数
    dataloader_config = config['dataloader']
    batch_size = batch_size or dataloader_config['batch_size']
    num_workers = num_workers or dataloader_config['num_workers']
    
    # 创建数据集
    dataset = MAEPretrainDataset(data_root, config)
    
    # 创建数据加载器
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=True,  # MAE预训练通常需要随机打乱
        num_workers=num_workers,
        pin_memory=dataloader_config.get('pin_memory', True),
        drop_last=dataloader_config.get('drop_last', True),
        collate_fn=collate_fn_mae,
        **kwargs
    )
    
    logger.info(f"MAE数据加载器创建完成: batch_size={batch_size}, "
               f"num_workers={num_workers}, samples={len(dataset)}")
    
    return dataloader


class LabeledMCGDataset(BaseMCGDataset):
    """
    有标签MCG数据集（预留给第二、三阶段使用）
    """
    
    def __init__(self, data_root: Union[str, Path], 
                 config: Dict[str, Any],
                 label_file: Union[str, Path],
                 split: str = 'train',
                 **kwargs):
        """
        有标签数据集初始化
        
        Args:
            data_root: 数据根目录
            config: 配置字典
            label_file: 标签Excel文件路径
            split: 数据分割
        """
        self.label_file = Path(label_file)
        
        super().__init__(
            data_root=data_root,
            config=config,
            split=split,
            **kwargs
        )
        
        # 加载标签数据
        self.labels = self._load_labels()
    
    def _load_labels(self) -> Dict[str, Any]:
        """
        加载标签数据（基于历史项目经验）
        
        Returns:
            标签字典 {sample_id: label_dict}
        """
        # 基于历史经验的标签加载逻辑
        logger.info(f"加载标签文件: {self.label_file}")
        
        # 读取Excel文件的多个sheet
        train_sheet = pd.read_excel(self.label_file, sheet_name='训练集')
        test_sheet = pd.read_excel(self.label_file, sheet_name='内测集')
        vessel_sheet = pd.read_excel(self.label_file, sheet_name='训测集三支信息')
        
        labels = {}
        
        # 处理血管狭窄信息
        for _, row in vessel_sheet.iterrows():
            sample_id = str(row['心磁号'])
            labels[sample_id] = {
                'LM': row.get('LM', 0),      # 左主干
                'LAD': row.get('LAD', 0),    # 左前降支  
                'LCX': row.get('LCX', 0),    # 左回旋支
                'RCA': row.get('RCA', 0),    # 右冠状动脉
                '综合诊断': row.get('造影结论', 0)  # 0/1二分类
            }
        
        # 合并临床特征（来自训练集和测试集）
        for sheet, sheet_name in [(train_sheet, 'train'), (test_sheet, 'test')]:
            for _, row in sheet.iterrows():
                sample_id = str(row['心磁号'])
                if sample_id in labels:
                    # 添加临床特征（12项特征）
                    clinical_features = {}
                    for col in sheet.columns:
                        if col != '心磁号':
                            clinical_features[col] = row.get(col, np.nan)
                    labels[sample_id]['clinical_features'] = clinical_features
                    labels[sample_id]['original_split'] = sheet_name
        
        logger.info(f"标签加载完成: {len(labels)}个样本")
        return labels
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """获取带标签的样本"""
        base_sample = super().__getitem__(idx)
        
        if base_sample['sample_id'] == 'empty':
            return base_sample
        
        sample_id = base_sample['sample_id']
        
        # 添加标签信息
        if sample_id in self.labels:
            label_info = self.labels[sample_id]
            
            # 血管狭窄程度（回归任务）
            vessel_scores = torch.tensor([
                label_info['LM'], label_info['LAD'], 
                label_info['LCX'], label_info['RCA']
            ], dtype=torch.float)
            
            # 综合诊断（分类任务）
            diagnosis = torch.tensor(label_info['综合诊断'], dtype=torch.long)
            
            # 临床特征处理
            clinical_features = self._process_clinical_features(
                label_info.get('clinical_features', {})
            )
            
            base_sample.update({
                'vessel_scores': vessel_scores,
                'diagnosis': diagnosis,
                'clinical_features': clinical_features
            })
        else:
            # 无标签样本，填充默认值
            base_sample.update({
                'vessel_scores': torch.zeros(4, dtype=torch.float),
                'diagnosis': torch.tensor(-1, dtype=torch.long),  # -1表示无标签
                'clinical_features': torch.zeros(12, dtype=torch.float)  # 假设12个临床特征
            })
        
        return base_sample
    
    def _process_clinical_features(self, clinical_dict: Dict) -> torch.Tensor:
        """
        处理临床特征（缺失值填充、编码等）
        
        Args:
            clinical_dict: 临床特征字典
            
        Returns:
            处理后的特征向量
        """
        # 这里需要根据具体的临床特征定义来实现
        # 包括缺失值填充、类别编码、数值归一化等
        
        # 简化实现：假设有12个数值特征
        features = []
        feature_names = list(clinical_dict.keys())[:12]  # 取前12个特征
        
        for name in feature_names:
            value = clinical_dict.get(name, np.nan)
            if pd.isna(value):
                value = 0.0  # 简单的缺失值填充
            features.append(float(value))
        
        # 补齐到12个特征
        while len(features) < 12:
            features.append(0.0)
        
        return torch.tensor(features[:12], dtype=torch.float)


# 便利函数
def create_datasets(data_root: Union[str, Path],
                   config: Dict[str, Any],
                   stage: int = 1,
                   label_file: Optional[Union[str, Path]] = None) -> Dict[str, Dataset]:
    """
    根据阶段创建相应的数据集
    
    Args:
        data_root: 数据根目录
        config: 配置字典
        stage: 项目阶段 (1: MAE预训练, 2-3: 有标签训练)
        label_file: 标签文件路径（阶段2-3需要）
        
    Returns:
        数据集字典
    """
    if stage == 1:
        # 第一阶段：MAE预训练
        datasets = {
            'train': MAEPretrainDataset(data_root, config, apply_augmentation=True)
        }
    else:
        # 第二、三阶段：有标签训练
        if label_file is None:
            raise ValueError("阶段2和3需要提供标签文件")
        
        datasets = {}
        for split in ['train', 'val', 'test']:
            datasets[split] = LabeledMCGDataset(
                data_root, config, label_file, split=split,
                apply_augmentation=(split == 'train')
            )
    
    return datasets


if __name__ == "__main__":
    # 测试代码
    import yaml
    
    # 加载配置
    config_path = Path(__file__).parent.parent.parent / "configs" / "preprocessing.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 测试MAE数据集
    data_root = config['data_config']['txt_data_dir']
    
    print("测试MAE预训练数据集...")
    dataset = MAEPretrainDataset(data_root, config, cache_processed=False)
    
    print(f"数据集大小: {len(dataset)}")
    
    # 测试单个样本
    sample = dataset[0]
    print(f"样本格式: {sample.keys()}")
    print(f"信号形状: {sample['signal'].shape}")
    print(f"目标形状: {sample['target'].shape}")
    
    # 测试数据加载器
    dataloader = create_mae_dataloader(data_root, config, batch_size=4, num_workers=0)
    batch = next(iter(dataloader))
    
    print(f"批次信号形状: {batch['signal'].shape}")
    print(f"批次目标形状: {batch['target'].shape}")
    print(f"批次大小: {len(batch['sample_ids'])}")
    
    # 统计信息
    stats = dataset.get_stats()
    print(f"数据集统计: {stats}")