"""
MCG数据预处理模块
================

基于历史项目经验的多策略数据预处理系统，特别注意避免常见陷阱：
1. 避免跨样本归一化导致的数据泄露
2. 样本级归一化保留个体特征
3. 时序长度统一的边界效应处理
4. 数值稳定性保证

支持的策略：
- 归一化: sample_level, global, channel_wise, robust
- Padding: reflect, edge, wrap, constant, resample  
- 噪声: 渐进式配置，从高斯噪声开始
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
import warnings
from scipy import stats
from sklearn.preprocessing import RobustScaler, QuantileTransformer
import logging

logger = logging.getLogger(__name__)


class MCGPreprocessor:
    """MCG数据预处理器 - 基于历史项目经验的多策略实现"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化预处理器
        
        Args:
            config: 预处理配置字典，来自preprocessing.yaml
        """
        self.config = config
        self.preprocessing_config = config['preprocessing']
        self.normalization_config = self.preprocessing_config['normalization']
        self.padding_config = self.preprocessing_config['padding']
        self.quality_config = self.preprocessing_config['quality_control']
        
        # 统计信息存储（用于global归一化）
        self.global_stats = None
        self.channel_stats = None
        self.is_fitted = False
        
        # 质量统计
        self.quality_stats = {
            'total_samples': 0,
            'valid_samples': 0,
            'filtered_samples': 0,
            'quality_issues': []
        }
        
    def load_mcg_signal(self, txt_path: Union[str, Path]) -> Optional[np.ndarray]:
        """
        加载单个MCG信号文件
        
        Args:
            txt_path: MCG .txt文件路径
            
        Returns:
            信号数据 [n_timestamp, 36] 或 None（如果加载失败）
        """
        try:
            # 基于历史经验的加载方法
            data = pd.read_csv(txt_path, sep='\t', header=None)
            
            # 验证数据格式
            if data.shape[1] < 37:  # 至少需要1列索引 + 36列通道
                logger.warning(f"数据格式错误 {txt_path}: 列数={data.shape[1]}")
                return None
            
            # 提取36个通道（忽略第一列索引）
            signal_data = data.iloc[:, 1:37].values
            
            # 数据类型转换和基本验证
            signal_data = signal_data.astype(np.float32)
            
            # 检查NaN值
            nan_ratio = np.isnan(signal_data).mean()
            if nan_ratio > self.quality_config['nan_threshold']:
                logger.warning(f"NaN值过多 {txt_path}: {nan_ratio:.2%}")
                return None
                
            # 检查时序长度
            seq_len = signal_data.shape[0]
            if (seq_len < self.quality_config['min_length'] or 
                seq_len > self.quality_config['max_length']):
                logger.warning(f"时序长度异常 {txt_path}: {seq_len}")
                return None
                
            return signal_data
            
        except Exception as e:
            logger.error(f"加载失败 {txt_path}: {e}")
            return None
    
    def detect_outliers(self, signal: np.ndarray) -> bool:
        """
        异常值检测
        
        Args:
            signal: MCG信号 [n_timestamp, 36]
            
        Returns:
            是否为异常样本
        """
        if not self.quality_config['outlier_detection']['enabled']:
            return False
            
        method = self.quality_config['outlier_detection']['method']
        threshold = self.quality_config['outlier_detection']['threshold']
        
        if method == 'iqr':
            # 四分位距方法
            q1 = np.percentile(signal, 25)
            q3 = np.percentile(signal, 75)
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            outlier_ratio = np.mean((signal < lower_bound) | (signal > upper_bound))
            return outlier_ratio > 0.1  # 10%以上异常点认为是异常样本
            
        elif method == 'zscore':
            # Z-score方法
            z_scores = np.abs(stats.zscore(signal, axis=None, nan_policy='omit'))
            outlier_ratio = np.mean(z_scores > threshold)
            return outlier_ratio > 0.05  # 5%以上异常点
            
        elif method == 'isolation_forest':
            # Isolation Forest (需要sklearn)
            from sklearn.ensemble import IsolationForest
            clf = IsolationForest(contamination=0.1, random_state=42)
            # 使用信号的统计特征进行检测
            features = np.array([
                signal.mean(axis=0),
                signal.std(axis=0),
                signal.min(axis=0),
                signal.max(axis=0)
            ]).flatten().reshape(1, -1)
            
            prediction = clf.fit_predict(features)
            return prediction[0] == -1  # -1表示异常
        
        return False
    
    def normalize_signal(self, signal: np.ndarray, 
                        sample_id: str = None) -> np.ndarray:
        """
        信号归一化 - 支持多种策略
        
        Args:
            signal: 输入信号 [n_timestamp, 36]
            sample_id: 样本ID（用于日志）
            
        Returns:
            归一化后的信号
        """
        strategy = self.normalization_config['strategy']
        method = self.normalization_config['method']
        params = self.normalization_config['params']
        
        # 处理NaN值
        if np.isnan(signal).any():
            # 使用通道内插值填补NaN
            signal = pd.DataFrame(signal).interpolate(method='linear').fillna(0).values
        
        if strategy == 'sample_level':
            # 样本级归一化 - 历史经验推荐
            return self._normalize_sample_level(signal, method, params)
            
        elif strategy == 'global':
            # 全局归一化 - 需要预先计算统计量
            if not self.is_fitted:
                raise ValueError("全局归一化需要先调用fit()方法")
            return self._normalize_global(signal, method)
            
        elif strategy == 'channel_wise':
            # 通道级归一化 - 每个通道独立
            return self._normalize_channel_wise(signal, method, params)
            
        elif strategy == 'robust':
            # 鲁棒归一化 - 抗异常值
            return self._normalize_robust(signal, params)
            
        else:
            raise ValueError(f"不支持的归一化策略: {strategy}")
    
    def _normalize_sample_level(self, signal: np.ndarray, 
                               method: str, params: Dict) -> np.ndarray:
        """样本级归一化 - 避免数据泄露，保留个体特征"""
        eps = params['eps']
        
        if method == 'standard':
            # 标准化：零均值，单位方差
            mean = signal.mean()
            std = signal.std()
            return (signal - mean) / (std + eps)
            
        elif method == 'minmax':
            # 最小-最大归一化
            min_val = signal.min()
            max_val = signal.max()
            return (signal - min_val) / (max_val - min_val + eps)
            
        elif method == 'maxabs':
            # 最大绝对值归一化
            max_abs = np.abs(signal).max()
            return signal / (max_abs + eps)
            
        elif method == 'quantile':
            # 分位数归一化
            q_low, q_high = params['quantile_range']
            low_val = np.percentile(signal, q_low * 100)
            high_val = np.percentile(signal, q_high * 100)
            return (signal - low_val) / (high_val - low_val + eps)
            
        else:
            raise ValueError(f"不支持的归一化方法: {method}")
    
    def _normalize_global(self, signal: np.ndarray, method: str) -> np.ndarray:
        """全局归一化 - 使用预计算的统计量"""
        if method == 'standard':
            return (signal - self.global_stats['mean']) / self.global_stats['std']
        elif method == 'minmax':
            return (signal - self.global_stats['min']) / (self.global_stats['max'] - self.global_stats['min'])
        else:
            raise ValueError(f"全局归一化不支持方法: {method}")
    
    def _normalize_channel_wise(self, signal: np.ndarray, 
                               method: str, params: Dict) -> np.ndarray:
        """通道级归一化 - 每个通道独立处理"""
        eps = params['eps']
        normalized = signal.copy()
        
        for ch in range(signal.shape[1]):
            channel_data = signal[:, ch]
            
            if method == 'standard':
                mean = channel_data.mean()
                std = channel_data.std()
                normalized[:, ch] = (channel_data - mean) / (std + eps)
            elif method == 'minmax':
                min_val = channel_data.min()
                max_val = channel_data.max()
                normalized[:, ch] = (channel_data - min_val) / (max_val - min_val + eps)
                
        return normalized
    
    def _normalize_robust(self, signal: np.ndarray, params: Dict) -> np.ndarray:
        """鲁棒归一化 - 抗异常值影响"""
        clip_range = params['clip_range']
        
        # 使用中位数和MAD (Median Absolute Deviation)
        median = np.median(signal)
        mad = np.median(np.abs(signal - median))
        
        # 鲁棒标准化
        normalized = (signal - median) / (1.4826 * mad + params['eps'])
        
        # 异常值裁剪
        normalized = np.clip(normalized, clip_range[0], clip_range[1])
        
        return normalized
    
    def unify_sequence_length(self, signal: np.ndarray) -> np.ndarray:
        """
        统一时序长度 - 基于历史经验的策略
        
        Args:
            signal: 输入信号 [n_timestamp, 36]
            
        Returns:
            长度统一后的信号 [target_length, 36]
        """
        target_length = self.padding_config['target_length']
        method = self.padding_config['method']
        truncate_mode = self.padding_config['truncate_mode']
        
        current_length = signal.shape[0]
        
        if current_length == target_length:
            return signal
        elif current_length > target_length:
            # 截断处理
            return self._truncate_signal(signal, target_length, truncate_mode)
        else:
            # 填充处理
            return self._pad_signal(signal, target_length, method)
    
    def _truncate_signal(self, signal: np.ndarray, target_length: int, 
                        mode: str) -> np.ndarray:
        """截断信号到目标长度"""
        current_length = signal.shape[0]
        
        if mode == 'start':
            return signal[:target_length]
        elif mode == 'end':
            return signal[-target_length:]
        elif mode == 'center':
            start_idx = (current_length - target_length) // 2
            return signal[start_idx:start_idx + target_length]
        elif mode == 'random':
            start_idx = np.random.randint(0, current_length - target_length + 1)
            return signal[start_idx:start_idx + target_length]
        else:
            raise ValueError(f"不支持的截断模式: {mode}")
    
    def _pad_signal(self, signal: np.ndarray, target_length: int, 
                   method: str) -> np.ndarray:
        """填充信号到目标长度"""
        current_length = signal.shape[0]
        pad_length = target_length - current_length
        
        if method == 'reflect':
            # 反射填充 - 历史经验推荐，保持信号连续性
            return np.pad(signal, ((0, pad_length), (0, 0)), mode='reflect')
        elif method == 'edge':
            # 边缘填充
            return np.pad(signal, ((0, pad_length), (0, 0)), mode='edge')
        elif method == 'wrap':
            # 循环填充
            return np.pad(signal, ((0, pad_length), (0, 0)), mode='wrap')
        elif method == 'constant':
            # 常数填充（零填充）
            return np.pad(signal, ((0, pad_length), (0, 0)), mode='constant')
        elif method == 'resample':
            # 重采样方法
            from scipy import signal as scipy_signal
            resampled = scipy_signal.resample(signal, target_length, axis=0)
            return resampled.astype(np.float32)
        else:
            raise ValueError(f"不支持的填充方法: {method}")
    
    def fit(self, signal_list: List[np.ndarray]):
        """
        拟合预处理器 - 计算全局统计量
        
        Args:
            signal_list: 信号列表，用于计算全局统计量
        """
        if self.normalization_config['strategy'] != 'global':
            logger.info("当前策略无需拟合，跳过")
            return
        
        logger.info("计算全局归一化统计量...")
        
        # 合并所有信号计算统计量
        all_signals = np.concatenate(signal_list, axis=0)
        
        self.global_stats = {
            'mean': all_signals.mean(),
            'std': all_signals.std(),
            'min': all_signals.min(),
            'max': all_signals.max()
        }
        
        self.is_fitted = True
        logger.info(f"全局统计量: mean={self.global_stats['mean']:.4f}, "
                   f"std={self.global_stats['std']:.4f}")
    
    def process_single(self, txt_path: Union[str, Path], 
                      sample_id: str = None) -> Optional[np.ndarray]:
        """
        处理单个MCG文件
        
        Args:
            txt_path: MCG文件路径
            sample_id: 样本ID
            
        Returns:
            预处理后的信号 [target_length, 36] 或 None
        """
        # 1. 加载信号
        signal = self.load_mcg_signal(txt_path)
        if signal is None:
            self.quality_stats['filtered_samples'] += 1
            return None
        
        self.quality_stats['total_samples'] += 1
        
        # 2. 质量检查
        if self.detect_outliers(signal):
            logger.warning(f"检测到异常样本: {sample_id or txt_path}")
            self.quality_stats['quality_issues'].append('outlier')
            self.quality_stats['filtered_samples'] += 1
            return None
        
        # 3. 时序长度统一
        signal = self.unify_sequence_length(signal)
        
        # 4. 信号归一化
        signal = self.normalize_signal(signal, sample_id)
        
        self.quality_stats['valid_samples'] += 1
        return signal
    
    def get_quality_report(self) -> Dict[str, Any]:
        """获取数据质量报告"""
        total = self.quality_stats['total_samples']
        valid = self.quality_stats['valid_samples']
        filtered = self.quality_stats['filtered_samples']
        
        return {
            'total_samples': total,
            'valid_samples': valid,
            'filtered_samples': filtered,
            'valid_ratio': valid / total if total > 0 else 0.0,
            'filter_ratio': filtered / total if total > 0 else 0.0,
            'quality_issues': self.quality_stats['quality_issues']
        }


class MCGAugmentor:
    """MCG数据增强器 - 渐进式噪声配置"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化增强器
        
        Args:
            config: 增强配置字典，来自preprocessing.yaml
        """
        self.config = config['augmentation']
        self.gaussian_config = self.config['gaussian_noise']
        self.powerline_config = self.config.get('powerline_noise', {})
        self.baseline_config = self.config.get('baseline_drift', {})
        
    def apply_augmentation(self, signal: np.ndarray) -> np.ndarray:
        """
        应用数据增强
        
        Args:
            signal: 输入信号 [target_length, 36]
            
        Returns:
            增强后的信号
        """
        augmented = signal.copy()
        
        # 1. 高斯噪声
        if (self.gaussian_config['enabled'] and 
            np.random.random() < self.gaussian_config['probability']):
            augmented = self._add_gaussian_noise(augmented)
        
        # 2. 工频干扰（预留）
        if (self.powerline_config.get('enabled', False) and
            np.random.random() < self.powerline_config.get('probability', 0)):
            augmented = self._add_powerline_noise(augmented)
        
        # 3. 基线漂移（预留）
        if (self.baseline_config.get('enabled', False) and
            np.random.random() < self.baseline_config.get('probability', 0)):
            augmented = self._add_baseline_drift(augmented)
        
        return augmented
    
    def _add_gaussian_noise(self, signal: np.ndarray) -> np.ndarray:
        """添加高斯噪声"""
        std_range = self.gaussian_config['std_range']
        noise_std = np.random.uniform(std_range[0], std_range[1])
        
        noise = np.random.normal(0, noise_std, signal.shape)
        return signal + noise.astype(np.float32)
    
    def _add_powerline_noise(self, signal: np.ndarray) -> np.ndarray:
        """添加工频干扰噪声（50/60Hz）"""
        frequencies = self.powerline_config['frequency']
        amplitude_range = self.powerline_config['amplitude_range']
        
        freq = np.random.choice(frequencies)
        amplitude = np.random.uniform(amplitude_range[0], amplitude_range[1])
        
        # 假设采样率，需要根据实际数据调整
        sampling_rate = 1000  # 1000 Hz
        t = np.linspace(0, signal.shape[0] / sampling_rate, signal.shape[0])
        
        # 同步添加到所有通道
        powerline_noise = amplitude * np.sin(2 * np.pi * freq * t)
        noise_matrix = powerline_noise[:, np.newaxis].repeat(signal.shape[1], axis=1)
        
        return signal + noise_matrix.astype(np.float32)
    
    def _add_baseline_drift(self, signal: np.ndarray) -> np.ndarray:
        """添加基线漂移"""
        drift_rate_range = self.baseline_config['drift_rate_range']
        drift_rate = np.random.uniform(drift_rate_range[0], drift_rate_range[1])
        
        # 线性漂移
        time_points = np.arange(signal.shape[0])
        drift = drift_rate * time_points
        drift_matrix = drift[:, np.newaxis].repeat(signal.shape[1], axis=1)
        
        return signal + drift_matrix.astype(np.float32)


# 便利函数
def load_and_preprocess_batch(file_paths: List[Path], 
                             config: Dict[str, Any],
                             apply_augmentation: bool = False) -> Tuple[List[np.ndarray], Dict]:
    """
    批量加载和预处理MCG文件
    
    Args:
        file_paths: MCG文件路径列表
        config: 配置字典
        apply_augmentation: 是否应用数据增强
        
    Returns:
        (processed_signals, quality_report)
    """
    preprocessor = MCGPreprocessor(config)
    augmentor = MCGAugmentor(config) if apply_augmentation else None
    
    processed_signals = []
    
    for file_path in file_paths:
        signal = preprocessor.process_single(file_path, str(file_path.stem))
        if signal is not None:
            if augmentor is not None:
                signal = augmentor.apply_augmentation(signal)
            processed_signals.append(signal)
    
    quality_report = preprocessor.get_quality_report()
    return processed_signals, quality_report


if __name__ == "__main__":
    # 测试代码
    import yaml
    
    # 加载配置
    config_path = Path(__file__).parent.parent.parent / "configs" / "preprocessing.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 测试预处理器
    data_dir = Path(config['data_config']['txt_data_dir'])
    sample_files = list(data_dir.glob("*.txt"))[:5]  # 取前5个文件测试
    
    print(f"测试预处理器，文件数: {len(sample_files)}")
    signals, report = load_and_preprocess_batch(sample_files, config, apply_augmentation=True)
    
    print(f"质量报告: {report}")
    print(f"处理成功: {len(signals)} 个信号")
    if signals:
        print(f"信号形状: {signals[0].shape}")