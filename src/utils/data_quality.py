"""
数据质量检查模块
包含对MCG数据的完整性、一致性和质量验证功能
"""

import numpy as np
import torch
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
import logging
import warnings
from scipy import stats
from scipy.signal import find_peaks
import matplotlib.pyplot as plt
import seaborn as sns

from ..data.preprocessing import MCGPreprocessor

logger = logging.getLogger(__name__)


@dataclass
class DataQualityReport:
    """数据质量报告"""
    total_files: int
    valid_files: int
    invalid_files: int
    file_size_stats: Dict[str, float]
    signal_quality_stats: Dict[str, float]
    channel_consistency: Dict[str, float]
    anomaly_detection: Dict[str, Any]
    temporal_consistency: Dict[str, float]
    missing_data_analysis: Dict[str, Any]
    recommendations: List[str]


class DataQualityChecker:
    """MCG数据质量检查器"""
    
    def __init__(self, 
                 data_dir: Union[str, Path],
                 sample_ratio: float = 0.1,
                 min_signal_length: int = 1000,
                 max_signal_length: int = 10000,
                 expected_channels: int = 36):
        """
        Args:
            data_dir: 数据目录路径
            sample_ratio: 抽样比例用于质量检查
            min_signal_length: 最小信号长度
            max_signal_length: 最大信号长度
            expected_channels: 期望的通道数
        """
        self.data_dir = Path(data_dir)
        self.sample_ratio = sample_ratio
        self.min_signal_length = min_signal_length
        self.max_signal_length = max_signal_length
        self.expected_channels = expected_channels
        
        self.preprocessor = MCGPreprocessor()
        self.quality_metrics = {}
        
    def check_file_structure(self, file_paths: List[Path]) -> Dict[str, Any]:
        """检查文件结构完整性"""
        results = {
            'total_files': len(file_paths),
            'readable_files': 0,
            'corrupted_files': [],
            'empty_files': [],
            'size_distribution': [],
        }
        
        for file_path in file_paths:
            try:
                if file_path.stat().st_size == 0:
                    results['empty_files'].append(str(file_path))
                    continue
                    
                # 尝试读取文件
                data = np.loadtxt(file_path)
                if data.size > 0:
                    results['readable_files'] += 1
                    results['size_distribution'].append(file_path.stat().st_size)
                else:
                    results['empty_files'].append(str(file_path))
                    
            except Exception as e:
                results['corrupted_files'].append({
                    'file': str(file_path),
                    'error': str(e)
                })
                
        return results
    
    def check_signal_dimensions(self, signals: List[np.ndarray]) -> Dict[str, Any]:
        """检查信号维度一致性"""
        shapes = [signal.shape for signal in signals]
        lengths = [shape[0] for shape in shapes]
        channels = [shape[1] if len(shape) > 1 else 1 for shape in shapes]
        
        return {
            'length_stats': {
                'mean': np.mean(lengths),
                'std': np.std(lengths),
                'min': np.min(lengths),
                'max': np.max(lengths),
                'median': np.median(lengths)
            },
            'channel_consistency': {
                'expected_channels': self.expected_channels,
                'actual_channels': channels,
                'consistent_channels': np.sum(np.array(channels) == self.expected_channels),
                'inconsistent_ratio': np.mean(np.array(channels) != self.expected_channels)
            },
            'length_outliers': [
                i for i, length in enumerate(lengths) 
                if length < self.min_signal_length or length > self.max_signal_length
            ]
        }
    
    def check_signal_quality(self, signals: List[np.ndarray]) -> Dict[str, Any]:
        """检查信号质量"""
        quality_metrics = {
            'snr_distribution': [],
            'amplitude_ranges': [],
            'zero_variance_channels': [],
            'extreme_values': [],
            'nan_inf_count': 0,
            'baseline_drift': []
        }
        
        for i, signal in enumerate(signals):
            if len(signal.shape) == 1:
                signal = signal.reshape(-1, 1)
                
            # 检查NaN和Inf值
            if np.any(np.isnan(signal)) or np.any(np.isinf(signal)):
                quality_metrics['nan_inf_count'] += 1
                
            # 信噪比估计
            signal_power = np.var(signal, axis=0)
            noise_power = np.var(np.diff(signal, axis=0), axis=0)
            snr = 10 * np.log10(signal_power / (noise_power + 1e-10))
            quality_metrics['snr_distribution'].extend(snr.tolist())
            
            # 幅值范围
            amplitude_range = np.ptp(signal, axis=0)
            quality_metrics['amplitude_ranges'].extend(amplitude_range.tolist())
            
            # 零方差通道检测
            zero_var_channels = np.where(np.var(signal, axis=0) < 1e-10)[0]
            if len(zero_var_channels) > 0:
                quality_metrics['zero_variance_channels'].append({
                    'signal_index': i,
                    'channels': zero_var_channels.tolist()
                })
            
            # 极值检测
            z_scores = np.abs(stats.zscore(signal, axis=0))
            extreme_points = np.where(z_scores > 5)
            if len(extreme_points[0]) > 0:
                quality_metrics['extreme_values'].append({
                    'signal_index': i,
                    'extreme_points': len(extreme_points[0])
                })
            
            # 基线漂移检测
            baseline_trend = np.polyfit(range(len(signal)), np.mean(signal, axis=1), 1)[0]
            quality_metrics['baseline_drift'].append(abs(baseline_trend))
            
        return quality_metrics
    
    def check_temporal_consistency(self, signals: List[np.ndarray]) -> Dict[str, Any]:
        """检查时间一致性"""
        temporal_metrics = {
            'sampling_rate_consistency': [],
            'temporal_artifacts': [],
            'frequency_content': []
        }
        
        for i, signal in enumerate(signals):
            if len(signal.shape) == 1:
                signal = signal.reshape(-1, 1)
                
            # 采样率一致性检查（基于信号长度和预期时间）
            # 这里假设我们有时间信息或固定采样率
            
            # 时间伪影检测（突然跳跃）
            diff_signal = np.diff(signal, axis=0)
            jump_threshold = 5 * np.std(diff_signal, axis=0)
            jumps = np.any(np.abs(diff_signal) > jump_threshold, axis=1)
            temporal_metrics['temporal_artifacts'].append(np.sum(jumps))
            
            # 频率内容分析
            fft_signal = np.fft.fft(signal, axis=0)
            power_spectrum = np.abs(fft_signal) ** 2
            dominant_freq_idx = np.argmax(power_spectrum[:len(power_spectrum)//2], axis=0)
            temporal_metrics['frequency_content'].extend(dominant_freq_idx.tolist())
            
        return temporal_metrics
    
    def detect_anomalies(self, signals: List[np.ndarray]) -> Dict[str, Any]:
        """异常检测"""
        anomaly_results = {
            'statistical_outliers': [],
            'pattern_anomalies': [],
            'correlation_anomalies': []
        }
        
        # 收集所有信号的统计特征
        all_features = []
        for signal in signals:
            if len(signal.shape) == 1:
                signal = signal.reshape(-1, 1)
                
            features = np.concatenate([
                np.mean(signal, axis=0),
                np.std(signal, axis=0),
                np.min(signal, axis=0),
                np.max(signal, axis=0),
                np.median(signal, axis=0)
            ])
            all_features.append(features)
            
        all_features = np.array(all_features)
        
        # 统计异常检测
        z_scores = np.abs(stats.zscore(all_features, axis=0))
        outlier_indices = np.where(np.any(z_scores > 3, axis=1))[0]
        anomaly_results['statistical_outliers'] = outlier_indices.tolist()
        
        # 相关性异常检测
        if len(signals) > 10:  # 需要足够的样本
            correlation_matrix = np.corrcoef(all_features)
            avg_correlation = np.mean(correlation_matrix, axis=1)
            corr_outliers = np.where(np.abs(avg_correlation - np.mean(avg_correlation)) > 2 * np.std(avg_correlation))[0]
            anomaly_results['correlation_anomalies'] = corr_outliers.tolist()
        
        return anomaly_results
    
    def analyze_missing_data(self, file_paths: List[Path]) -> Dict[str, Any]:
        """分析缺失数据模式"""
        missing_analysis = {
            'missing_files': [],
            'incomplete_signals': [],
            'missing_patterns': {}
        }
        
        expected_pattern = r'.*\.txt$'  # 根据实际文件命名模式调整
        
        # 检查文件命名连续性或其他预期模式
        # 这里可以根据具体的文件命名规则来实现
        
        return missing_analysis
    
    def generate_recommendations(self, 
                               file_check: Dict[str, Any],
                               dimension_check: Dict[str, Any],
                               quality_check: Dict[str, Any],
                               temporal_check: Dict[str, Any],
                               anomaly_check: Dict[str, Any]) -> List[str]:
        """生成数据质量改进建议"""
        recommendations = []
        
        # 文件完整性建议
        if len(file_check['corrupted_files']) > 0:
            recommendations.append(f"发现{len(file_check['corrupted_files'])}个损坏文件，需要重新获取或修复")
            
        if len(file_check['empty_files']) > 0:
            recommendations.append(f"发现{len(file_check['empty_files'])}个空文件，建议删除或重新采集")
        
        # 维度一致性建议
        inconsistent_ratio = dimension_check['channel_consistency']['inconsistent_ratio']
        if inconsistent_ratio > 0.05:
            recommendations.append(f"通道数不一致的文件比例为{inconsistent_ratio:.2%}，建议标准化数据格式")
        
        # 信号质量建议
        snr_values = quality_check['snr_distribution']
        if len(snr_values) > 0:
            avg_snr = np.mean(snr_values)
            if avg_snr < 10:
                recommendations.append(f"平均信噪比为{avg_snr:.2f}dB，建议增强信号预处理")
        
        if quality_check['nan_inf_count'] > 0:
            recommendations.append(f"发现{quality_check['nan_inf_count']}个文件包含NaN/Inf值，需要数据清洗")
        
        if len(quality_check['zero_variance_channels']) > 0:
            recommendations.append("发现零方差通道，可能是传感器故障，建议检查硬件")
        
        # 时间一致性建议
        if len(temporal_check['temporal_artifacts']) > 0:
            avg_artifacts = np.mean(temporal_check['temporal_artifacts'])
            if avg_artifacts > 10:
                recommendations.append("发现较多时间伪影，建议检查采集系统稳定性")
        
        # 异常检测建议
        if len(anomaly_check['statistical_outliers']) > len(file_check) * 0.05:
            recommendations.append("统计异常样本较多，建议进行详细的异常分析")
            
        return recommendations
    
    def run_quality_check(self, max_files: Optional[int] = None) -> DataQualityReport:
        """执行完整的数据质量检查"""
        logger.info("开始数据质量检查...")
        
        # 获取所有数据文件
        file_paths = list(self.data_dir.glob("*.txt"))
        if max_files:
            # 随机抽样
            np.random.seed(42)
            indices = np.random.choice(len(file_paths), 
                                     min(max_files, len(file_paths)), 
                                     replace=False)
            file_paths = [file_paths[i] for i in indices]
        
        logger.info(f"检查{len(file_paths)}个数据文件...")
        
        # 1. 文件结构检查
        logger.info("检查文件结构...")
        file_check = self.check_file_structure(file_paths)
        
        # 2. 加载有效信号数据
        logger.info("加载信号数据...")
        signals = []
        valid_files = []
        
        for file_path in file_paths[:min(100, len(file_paths))]:  # 限制内存使用
            try:
                signal = np.loadtxt(file_path)
                if signal.size > 0:
                    signals.append(signal)
                    valid_files.append(file_path)
            except:
                continue
        
        if not signals:
            logger.warning("没有找到有效的信号数据")
            return DataQualityReport(
                total_files=len(file_paths),
                valid_files=0,
                invalid_files=len(file_paths),
                file_size_stats={},
                signal_quality_stats={},
                channel_consistency={},
                anomaly_detection={},
                temporal_consistency={},
                missing_data_analysis={},
                recommendations=["没有找到有效数据文件"]
            )
        
        # 3. 维度检查
        logger.info("检查信号维度...")
        dimension_check = self.check_signal_dimensions(signals)
        
        # 4. 信号质量检查
        logger.info("检查信号质量...")
        quality_check = self.check_signal_quality(signals)
        
        # 5. 时间一致性检查
        logger.info("检查时间一致性...")
        temporal_check = self.check_temporal_consistency(signals)
        
        # 6. 异常检测
        logger.info("进行异常检测...")
        anomaly_check = self.detect_anomalies(signals)
        
        # 7. 缺失数据分析
        logger.info("分析缺失数据...")
        missing_analysis = self.analyze_missing_data(file_paths)
        
        # 8. 生成建议
        recommendations = self.generate_recommendations(
            file_check, dimension_check, quality_check,
            temporal_check, anomaly_check
        )
        
        # 构建报告
        report = DataQualityReport(
            total_files=len(file_paths),
            valid_files=len(valid_files),
            invalid_files=len(file_paths) - len(valid_files),
            file_size_stats={
                'mean_size': np.mean(file_check['size_distribution']) if file_check['size_distribution'] else 0,
                'std_size': np.std(file_check['size_distribution']) if file_check['size_distribution'] else 0
            },
            signal_quality_stats={
                'avg_snr': np.mean(quality_check['snr_distribution']) if quality_check['snr_distribution'] else 0,
                'avg_amplitude_range': np.mean(quality_check['amplitude_ranges']) if quality_check['amplitude_ranges'] else 0,
                'zero_variance_ratio': len(quality_check['zero_variance_channels']) / len(signals) if signals else 0,
                'nan_inf_ratio': quality_check['nan_inf_count'] / len(signals) if signals else 0
            },
            channel_consistency=dimension_check['channel_consistency'],
            anomaly_detection=anomaly_check,
            temporal_consistency={
                'avg_temporal_artifacts': np.mean(temporal_check['temporal_artifacts']) if temporal_check['temporal_artifacts'] else 0
            },
            missing_data_analysis=missing_analysis,
            recommendations=recommendations
        )
        
        logger.info("数据质量检查完成")
        return report
    
    def save_quality_report(self, report: DataQualityReport, output_path: Union[str, Path]):
        """保存质量报告"""
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存为JSON格式
        import json
        with open(output_path.with_suffix('.json'), 'w', encoding='utf-8') as f:
            json.dump(report.__dict__, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存为可读文本格式
        with open(output_path.with_suffix('.txt'), 'w', encoding='utf-8') as f:
            f.write("MCG数据质量检查报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"总文件数: {report.total_files}\n")
            f.write(f"有效文件数: {report.valid_files}\n")
            f.write(f"无效文件数: {report.invalid_files}\n")
            f.write(f"有效率: {report.valid_files/report.total_files*100:.2f}%\n\n")
            
            f.write("信号质量统计:\n")
            f.write(f"  平均信噪比: {report.signal_quality_stats.get('avg_snr', 0):.2f} dB\n")
            f.write(f"  平均幅值范围: {report.signal_quality_stats.get('avg_amplitude_range', 0):.2f}\n")
            f.write(f"  零方差通道比例: {report.signal_quality_stats.get('zero_variance_ratio', 0)*100:.2f}%\n")
            f.write(f"  NaN/Inf值比例: {report.signal_quality_stats.get('nan_inf_ratio', 0)*100:.2f}%\n\n")
            
            f.write("通道一致性:\n")
            f.write(f"  期望通道数: {report.channel_consistency.get('expected_channels', 0)}\n")
            f.write(f"  一致通道文件数: {report.channel_consistency.get('consistent_channels', 0)}\n")
            f.write(f"  不一致比例: {report.channel_consistency.get('inconsistent_ratio', 0)*100:.2f}%\n\n")
            
            f.write("改进建议:\n")
            for i, rec in enumerate(report.recommendations, 1):
                f.write(f"  {i}. {rec}\n")
                
        logger.info(f"质量报告已保存至: {output_path}")


class DataIntegrityValidator:
    """数据完整性验证器"""
    
    def __init__(self):
        self.validation_rules = {}
        
    def add_validation_rule(self, name: str, rule_func: callable, description: str):
        """添加验证规则"""
        self.validation_rules[name] = {
            'func': rule_func,
            'description': description
        }
    
    def validate_signal(self, signal: np.ndarray, metadata: Optional[Dict] = None) -> Dict[str, Any]:
        """验证单个信号"""
        results = {}
        for name, rule in self.validation_rules.items():
            try:
                result = rule['func'](signal, metadata)
                results[name] = {
                    'passed': result,
                    'description': rule['description']
                }
            except Exception as e:
                results[name] = {
                    'passed': False,
                    'error': str(e),
                    'description': rule['description']
                }
        return results
    
    def validate_dataset(self, signals: List[np.ndarray]) -> Dict[str, Any]:
        """验证整个数据集"""
        validation_summary = {
            'total_signals': len(signals),
            'validation_results': [],
            'overall_pass_rate': {},
            'failed_signals': []
        }
        
        for i, signal in enumerate(signals):
            result = self.validate_signal(signal)
            validation_summary['validation_results'].append({
                'signal_index': i,
                'results': result
            })
            
            # 记录失败的信号
            failed_rules = [name for name, res in result.items() if not res['passed']]
            if failed_rules:
                validation_summary['failed_signals'].append({
                    'signal_index': i,
                    'failed_rules': failed_rules
                })
        
        # 计算总体通过率
        for rule_name in self.validation_rules.keys():
            passed = sum(1 for result in validation_summary['validation_results'] 
                        if result['results'][rule_name]['passed'])
            validation_summary['overall_pass_rate'][rule_name] = passed / len(signals)
            
        return validation_summary


# 预定义验证规则
def validate_signal_length(signal: np.ndarray, metadata: Optional[Dict] = None) -> bool:
    """验证信号长度"""
    min_length = metadata.get('min_length', 1000) if metadata else 1000
    max_length = metadata.get('max_length', 10000) if metadata else 10000
    return min_length <= len(signal) <= max_length

def validate_channel_count(signal: np.ndarray, metadata: Optional[Dict] = None) -> bool:
    """验证通道数"""
    expected_channels = metadata.get('expected_channels', 36) if metadata else 36
    if len(signal.shape) == 1:
        return expected_channels == 1
    return signal.shape[1] == expected_channels

def validate_no_nan_inf(signal: np.ndarray, metadata: Optional[Dict] = None) -> bool:
    """验证无NaN/Inf值"""
    return not (np.any(np.isnan(signal)) or np.any(np.isinf(signal)))

def validate_signal_range(signal: np.ndarray, metadata: Optional[Dict] = None) -> bool:
    """验证信号范围"""
    min_val = metadata.get('min_value', -1000) if metadata else -1000
    max_val = metadata.get('max_value', 1000) if metadata else 1000
    return np.all((signal >= min_val) & (signal <= max_val))

def validate_variance_threshold(signal: np.ndarray, metadata: Optional[Dict] = None) -> bool:
    """验证方差阈值"""
    min_variance = metadata.get('min_variance', 1e-6) if metadata else 1e-6
    if len(signal.shape) == 1:
        return np.var(signal) >= min_variance
    return np.all(np.var(signal, axis=0) >= min_variance)