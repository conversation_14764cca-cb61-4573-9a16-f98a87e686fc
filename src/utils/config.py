"""
配置管理系统
============

为MCG三阶段项目提供统一的配置管理：
- 层次化配置加载和验证
- 环境变量支持
- 配置合并和继承
- 实验配置模板
- 配置版本管理
"""

import yaml
import json
import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
import copy
from datetime import datetime
import hashlib

logger = logging.getLogger(__name__)


@dataclass
class ExperimentInfo:
    """实验信息"""
    name: str
    description: str
    stage: int
    version: str
    created_at: str
    config_hash: str
    data_path: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_data_config(config: Dict[str, Any]) -> List[str]:
        """验证数据配置"""
        errors = []
        
        data_config = config.get('data_config', {})
        
        # 检查必需字段
        required_fields = ['txt_data_dir', 'channels', 'target_length']
        for field in required_fields:
            if field not in data_config:
                errors.append(f"缺少必需的数据配置字段: {field}")
        
        # 检查数据路径
        if 'txt_data_dir' in data_config:
            data_path = Path(data_config['txt_data_dir'])
            if not data_path.exists():
                errors.append(f"数据路径不存在: {data_path}")
        
        # 检查通道数
        if 'channels' in data_config:
            channels = data_config['channels']
            if channels != 36:
                errors.append(f"MCG通道数应为36，当前为: {channels}")
        
        return errors
    
    @staticmethod
    def validate_preprocessing_config(config: Dict[str, Any]) -> List[str]:
        """验证预处理配置"""
        errors = []
        
        preprocessing = config.get('preprocessing', {})
        
        # 验证归一化配置
        norm_config = preprocessing.get('normalization', {})
        valid_strategies = ['sample_level', 'global', 'channel_wise', 'robust', 'dataset_level']
        valid_methods = ['standard', 'minmax', 'maxabs', 'quantile', 'robust_scale']
        
        strategy = norm_config.get('strategy')
        if strategy and strategy not in valid_strategies:
            errors.append(f"无效的归一化策略: {strategy}，支持的策略: {valid_strategies}")
        
        method = norm_config.get('method')
        if method and method not in valid_methods:
            errors.append(f"无效的归一化方法: {method}，支持的方法: {valid_methods}")
        
        # 验证padding配置
        padding_config = preprocessing.get('padding', {})
        valid_methods = ['reflect', 'edge', 'wrap', 'constant', 'resample']
        
        padding_method = padding_config.get('method')
        if padding_method and padding_method not in valid_methods:
            errors.append(f"无效的padding方法: {padding_method}，支持的方法: {valid_methods}")
        
        return errors
    
    @staticmethod
    def validate_model_config(config: Dict[str, Any]) -> List[str]:
        """验证模型配置"""
        errors = []
        
        model_config = config.get('model', {})
        
        # 验证编码器配置
        encoder_config = model_config.get('encoder', {})
        if not encoder_config:
            errors.append("缺少编码器配置")
        
        # 验证掩码配置
        masking_config = model_config.get('masking', {})
        valid_mask_types = [
            'temporal_block', 'spatial_block', 'joint_spatiotemporal',
            'structured', 'adaptive', 'progressive'
        ]
        
        mask_type = masking_config.get('type')
        if mask_type and mask_type not in valid_mask_types:
            errors.append(f"无效的掩码类型: {mask_type}，支持的类型: {valid_mask_types}")
        
        mask_ratio = masking_config.get('mask_ratio', 0.2)
        if not (0.05 <= mask_ratio <= 0.8):
            errors.append(f"掩码比例应在0.05-0.8之间，当前为: {mask_ratio}")
        
        return errors
    
    @staticmethod
    def validate_training_config(config: Dict[str, Any]) -> List[str]:
        """验证训练配置"""
        errors = []
        
        training_config = config.get('training', {})
        
        # 验证优化器配置
        optimizer_config = training_config.get('optimizer', {})
        valid_optimizers = ['AdamW', 'Adam', 'SGD']
        
        optimizer_type = optimizer_config.get('type')
        if optimizer_type and optimizer_type not in valid_optimizers:
            errors.append(f"无效的优化器类型: {optimizer_type}，支持的类型: {valid_optimizers}")
        
        # 验证学习率
        lr = optimizer_config.get('lr', 1e-4)
        if not (1e-6 <= lr <= 1e-1):
            errors.append(f"学习率应在1e-6到1e-1之间，当前为: {lr}")
        
        # 验证训练轮数
        epochs = training_config.get('epochs', 100)
        if not (1 <= epochs <= 1000):
            errors.append(f"训练轮数应在1-1000之间，当前为: {epochs}")
        
        return errors


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, base_config_dir: Union[str, Path] = None):
        """
        初始化配置管理器
        
        Args:
            base_config_dir: 基础配置目录
        """
        if base_config_dir is None:
            # 自动检测项目根目录
            current_file = Path(__file__).resolve()
            project_root = current_file.parent.parent.parent
            self.base_config_dir = project_root / 'configs'
        else:
            self.base_config_dir = Path(base_config_dir)
        
        self.base_config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置缓存
        self._config_cache = {}
        
        # 默认配置
        self._default_config = self._load_default_config()
        
        logger.info(f"配置管理器初始化，配置目录: {self.base_config_dir}")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'data_config': {
                'txt_data_dir': '/home/<USER>/Wangmeng/data/data0318',
                'channels': 36,
                'target_length': 1500
            },
            'preprocessing': {
                'normalization': {
                    'strategy': 'sample_level',
                    'method': 'standard',
                    'params': {
                        'eps': 1e-8,
                        'clip_range': [-10, 10],
                        'quantile_range': [0.05, 0.95]
                    }
                },
                'padding': {
                    'target_length': 1500,
                    'method': 'reflect',
                    'truncate_mode': 'center'
                },
                'quality_control': {
                    'min_length': 300,
                    'max_length': 3000,
                    'nan_threshold': 0.05,
                    'outlier_detection': {
                        'enabled': True,
                        'method': 'iqr',
                        'threshold': 3.0
                    }
                }
            },
            'augmentation': {
                'gaussian_noise': {
                    'enabled': True,
                    'std_range': [0.01, 0.05],
                    'probability': 0.3
                }
            },
            'model': {
                'encoder': {
                    'hidden_dim': 512,
                    'spatial_encoder': {
                        'type': 'mcg_spatial',
                        'conv_channels': [64, 128, 256],
                        'dropout': 0.1
                    },
                    'temporal_encoder': {
                        'type': 'mcg_temporal',
                        'd_model': 256,
                        'nhead': 8,
                        'num_layers': 4,
                        'dropout': 0.1
                    },
                    'fusion_module': {
                        'fusion_type': 'cross_attention',
                        'hidden_dim': 512,
                        'dropout': 0.1
                    }
                },
                'decoder': {
                    'decoder_dim': 256,
                    'num_layers': 2,
                    'dropout': 0.1
                },
                'masking': {
                    'type': 'temporal_block',
                    'mask_ratio': 0.25,
                    'block_size_range': [50, 100],
                    'num_blocks_range': [1, 3]
                },
                'loss_type': 'mse',
                'normalize_target': True,
                'mask_loss_only': True
            },
            'training': {
                'optimizer': {
                    'type': 'AdamW',
                    'lr': 1e-4,
                    'weight_decay': 0.05,
                    'betas': [0.9, 0.95]
                },
                'scheduler': {
                    'enabled': True,
                    'type': 'cosine_with_warmup',
                    'warmup_ratio': 0.1
                },
                'epochs': 100,
                'gradient_clip_norm': 1.0,
                'gradient_accumulation_steps': 1,
                'mixed_precision': True,
                'early_stopping': {
                    'patience': 15,
                    'min_delta': 1e-4
                }
            },
            'dataloader': {
                'batch_size': 32,
                'num_workers': 4,
                'pin_memory': True,
                'drop_last': True
            },
            'evaluation': {
                'val_interval': 2,
                'save_interval': 5,
                'log_interval': 100
            },
            'system': {
                'device': 'cuda',
                'random_seed': 42,
                'deterministic': True
            }
        }
    
    def load_config(self, config_path: Union[str, Path], 
                   merge_default: bool = True) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            merge_default: 是否与默认配置合并
            
        Returns:
            配置字典
        """
        config_path = Path(config_path)
        
        # 检查缓存
        cache_key = str(config_path)
        if cache_key in self._config_cache:
            logger.debug(f"从缓存加载配置: {config_path}")
            return self._config_cache[cache_key]
        
        # 加载配置文件
        if not config_path.exists():
            # 尝试在基础配置目录中查找
            config_path = self.base_config_dir / config_path
            if not config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    config = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    config = json.load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
        except Exception as e:
            raise ValueError(f"配置文件解析失败 {config_path}: {e}")
        
        # 环境变量替换
        config = self._substitute_env_vars(config)
        
        # 与默认配置合并
        if merge_default:
            config = self._deep_merge(self._default_config, config)
        
        # 缓存配置
        self._config_cache[cache_key] = config
        
        logger.info(f"配置文件已加载: {config_path}")
        return config
    
    def save_config(self, config: Dict[str, Any], 
                   save_path: Union[str, Path],
                   format: str = 'yaml') -> None:
        """
        保存配置文件
        
        Args:
            config: 配置字典
            save_path: 保存路径
            format: 文件格式 ['yaml', 'json']
        """
        save_path = Path(save_path)
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                if format.lower() == 'yaml':
                    yaml.dump(config, f, default_flow_style=False, 
                             allow_unicode=True, sort_keys=False)
                elif format.lower() == 'json':
                    json.dump(config, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的格式: {format}")
        except Exception as e:
            raise ValueError(f"配置文件保存失败 {save_path}: {e}")
        
        logger.info(f"配置文件已保存: {save_path}")
    
    def validate_config(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证配置
        
        Args:
            config: 配置字典
            
        Returns:
            (is_valid, error_messages)
        """
        errors = []
        
        # 各个模块的验证
        errors.extend(ConfigValidator.validate_data_config(config))
        errors.extend(ConfigValidator.validate_preprocessing_config(config))
        errors.extend(ConfigValidator.validate_model_config(config))
        errors.extend(ConfigValidator.validate_training_config(config))
        
        is_valid = len(errors) == 0
        return is_valid, errors
    
    def create_experiment_config(self, experiment_name: str,
                               base_config: Optional[Dict[str, Any]] = None,
                               overrides: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        创建实验配置
        
        Args:
            experiment_name: 实验名称
            base_config: 基础配置
            overrides: 覆盖配置
            
        Returns:
            实验配置
        """
        if base_config is None:
            base_config = copy.deepcopy(self._default_config)
        
        # 应用覆盖配置
        if overrides:
            base_config = self._deep_merge(base_config, overrides)
        
        # 添加实验信息
        experiment_info = {
            'experiment': {
                'name': experiment_name,
                'description': f'MAE预训练实验: {experiment_name}',
                'stage': 1,
                'version': '1.0.0',
                'created_at': datetime.now().isoformat(),
                'config_hash': self._compute_config_hash(base_config)
            }
        }
        
        base_config.update(experiment_info)
        
        return base_config
    
    def get_config_templates(self) -> Dict[str, Dict[str, Any]]:
        """获取配置模板"""
        templates = {
            'mae_small': {
                'model': {
                    'encoder': {'hidden_dim': 256},
                    'decoder': {'decoder_dim': 128},
                    'masking': {'mask_ratio': 0.15}
                },
                'training': {
                    'optimizer': {'lr': 2e-4},
                    'epochs': 50
                }
            },
            'mae_base': {
                'model': {
                    'encoder': {'hidden_dim': 512},
                    'decoder': {'decoder_dim': 256},
                    'masking': {'mask_ratio': 0.25}
                },
                'training': {
                    'optimizer': {'lr': 1e-4},
                    'epochs': 100
                }
            },
            'mae_large': {
                'model': {
                    'encoder': {'hidden_dim': 768},
                    'decoder': {'decoder_dim': 384},
                    'masking': {'mask_ratio': 0.35}
                },
                'training': {
                    'optimizer': {'lr': 5e-5},
                    'epochs': 150
                }
            },
            # 不同掩码策略的模板
            'temporal_masking': {
                'model': {
                    'masking': {
                        'type': 'temporal_block',
                        'mask_ratio': 0.2,
                        'block_size_range': [50, 100]
                    }
                }
            },
            'spatial_masking': {
                'model': {
                    'masking': {
                        'type': 'spatial_block',
                        'mask_ratio': 0.15,
                        'block_shapes': [[2, 2], [3, 3]]
                    }
                }
            },
            'joint_masking': {
                'model': {
                    'masking': {
                        'type': 'joint_spatiotemporal',
                        'mask_ratio': 0.25,
                        'temporal_ratio': 0.6,
                        'spatial_ratio': 0.4
                    }
                }
            }
        }
        
        return templates
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并字典"""
        result = copy.deepcopy(base)
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _substitute_env_vars(self, config: Any) -> Any:
        """替换环境变量"""
        if isinstance(config, dict):
            return {k: self._substitute_env_vars(v) for k, v in config.items()}
        elif isinstance(config, list):
            return [self._substitute_env_vars(item) for item in config]
        elif isinstance(config, str) and config.startswith('${') and config.endswith('}'):
            env_var = config[2:-1]
            if ':-' in env_var:
                var_name, default_value = env_var.split(':-', 1)
                return os.getenv(var_name, default_value)
            else:
                return os.getenv(env_var, config)
        else:
            return config
    
    def _compute_config_hash(self, config: Dict[str, Any]) -> str:
        """计算配置哈希值"""
        config_str = json.dumps(config, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()


# 便利函数
def load_config(config_path: Union[str, Path]) -> Dict[str, Any]:
    """加载配置的便利函数"""
    manager = ConfigManager()
    return manager.load_config(config_path)


def create_experiment_config(experiment_name: str,
                           template: str = 'mae_base',
                           overrides: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """创建实验配置的便利函数"""
    manager = ConfigManager()
    
    # 获取模板
    templates = manager.get_config_templates()
    if template not in templates:
        raise ValueError(f"未知的模板: {template}，可用模板: {list(templates.keys())}")
    
    template_config = templates[template]
    
    # 合并覆盖配置
    if overrides:
        template_config = manager._deep_merge(template_config, overrides)
    
    return manager.create_experiment_config(experiment_name, overrides=template_config)


if __name__ == "__main__":
    # 测试配置管理器
    manager = ConfigManager()
    
    print("默认配置加载测试:")
    default_config = manager._default_config
    print(f"  - 数据通道数: {default_config['data_config']['channels']}")
    print(f"  - 模型隐藏维度: {default_config['model']['encoder']['hidden_dim']}")
    print(f"  - 掩码比例: {default_config['model']['masking']['mask_ratio']}")
    
    # 测试配置验证
    print("\n配置验证测试:")
    is_valid, errors = manager.validate_config(default_config)
    print(f"  - 配置有效: {is_valid}")
    if errors:
        print(f"  - 错误信息: {errors}")
    
    # 测试实验配置创建
    print("\n实验配置创建测试:")
    exp_config = create_experiment_config(
        'test_mae_experiment',
        template='mae_base',
        overrides={
            'training': {'epochs': 50},
            'model': {'masking': {'mask_ratio': 0.3}}
        }
    )
    
    print(f"  - 实验名称: {exp_config['experiment']['name']}")
    print(f"  - 训练轮数: {exp_config['training']['epochs']}")
    print(f"  - 掩码比例: {exp_config['model']['masking']['mask_ratio']}")
    
    # 测试配置模板
    print("\n可用配置模板:")
    templates = manager.get_config_templates()
    for name in templates.keys():
        print(f"  - {name}")
    
    print("\n配置管理器测试完成！")