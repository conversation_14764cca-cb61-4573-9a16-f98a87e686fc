#!/usr/bin/env python3
"""
MCG Phase 1 MAE预训练 - 最小化版本
================================

使用最小配置确保训练能够运行
"""

import os
import sys
import torch
import logging
from pathlib import Path
from datetime import datetime

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def main():
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 创建实验目录
    exp_dir = Path(f"experiments/mae_minimal_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    exp_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"实验目录: {exp_dir}")
    
    # 最小化配置
    config = {
        # 数据配置
        'data_config': {
            'txt_data_dir': '/home/<USER>/Wangmeng/data/data0318',
            'target_length': 1000,  # 缩短序列长度
            'channels': 36
        },
        'data_split': {
            'train_ratio': 1.0,
            'val_ratio': 0.0,
            'random_seed': 42
        },
        'preprocessing': {
            'normalization': {
                'strategy': 'sample_level',
                'method': 'standard',
                'params': {'eps': 1e-8}
            },
            'padding': {
                'target_length': 1000,
                'method': 'reflect',
                'truncate_mode': 'center'
            },
            'quality_control': {
                'nan_threshold': 0.1,
                'min_length': 500,
                'max_length': 2000,
                'outlier_detection': {'enabled': False}
            }
        },
        'augmentation': {
            'gaussian_noise': {
                'enabled': True,
                'probability': 0.3,
                'std_range': [0.01, 0.05]
            }
        },
        'dataloader': {
            'batch_size': 1,
            'num_workers': 0,
            'pin_memory': False,
            'drop_last': False
        },
        
        # 极简模型配置
        'model': {
            'encoder': {
                'hidden_dim': 32,
                'spatial_encoder': {
                    'type': 'mcg_spatial',
                    'input_channels': 36,
                    'conv_channels': [16, 32],  # 只有2层
                    'dropout': 0.0,
                    'pooling': 'adaptive',
                    'use_attention': False
                },
                'temporal_encoder': {
                    'type': 'mcg_temporal',
                    'd_model': 32,  # 匹配hidden_dim
                    'nhead': 2,
                    'num_layers': 1,  # 只有1层
                    'dropout': 0.0,
                    'global_pooling': 'attention'
                },
                'fusion_module': {
                    'fusion_type': 'cross_attention',
                    'spatial_dim': 32,  # 匹配空间编码器最后一层
                    'temporal_dim': 32,  # 匹配时间编码器d_model
                    'hidden_dim': 32,
                    'dropout': 0.0
                }
            },
            'decoder': {
                'encoder_dim': 32,
                'decoder_dim': 16,
                'num_heads': 2,
                'num_layers': 1,
                'output_channels': 36,
                'output_seq_len': 1000
            },
            'masking': {
                'type': 'temporal_block',
                'mask_ratio': 0.2,
                'block_size_range': [10, 50]
            },
            'loss_type': 'mse',
            'normalize_target': True,
            'mask_loss_only': True
        },
        
        # 训练配置
        'epochs': 1,  # 只训练1个epoch
        'optimizer': {
            'type': 'AdamW',
            'lr': 1e-4,
            'weight_decay': 0.01,
            'betas': [0.9, 0.95]
        },
        'scheduler': {
            'enabled': True,
            'type': 'cosine',
            'min_lr': 1e-6
        },
        'mixed_precision': False,  # 关闭混合精度
        'gradient_clip_norm': 1.0,
        'gradient_accumulation_steps': 1,
        'eval_interval': 1000,  # 很少验证
        'save_interval': 1000,
        'log_interval': 100,
        'early_stopping': {'patience': 10, 'min_delta': 1e-4},
        'validation': {'enabled': False},  # 关闭验证
        'experiment_dir': str(exp_dir),
        'random_seed': 42,
        'device': 'cuda'
    }
    
    # 设置随机种子
    torch.manual_seed(config['random_seed'])
    
    try:
        from training.mae_trainer import create_mae_trainer
        
        data_root = config['data_config']['txt_data_dir']
        logger.info(f"数据路径: {data_root}")
        
        # 创建训练器
        trainer = create_mae_trainer(config, data_root)
        
        logger.info("开始最小化MAE预训练...")
        logger.info(f"模型参数量: {sum(p.numel() for p in trainer.model.parameters()):,}")
        
        # 开始训练
        trainer.train()
        
        logger.info("训练完成！")
        
    except Exception as e:
        logger.error(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
