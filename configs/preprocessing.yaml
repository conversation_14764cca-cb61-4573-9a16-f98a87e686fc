# MCG数据预处理配置
# 支持多种策略，确保第一阶段质量，预留后续阶段扩展性

data_config:
  # 原始数据路径
  txt_data_dir: "/home/<USER>/Wangmeng/data/data0318"
  channels: 36
  target_length: 1500
  
preprocessing:
  # 归一化策略 - 多选择配置
  normalization:
    strategy: "sample_level"  # ["sample_level", "global", "channel_wise", "robust"]
    method: "standard"        # ["standard", "minmax", "maxabs", "quantile"]
    params:
      eps: 1e-8              # 数值稳定性常数
      clip_range: [-10, 10]  # 异常值裁剪范围 (适用于robust策略)
      quantile_range: [0.05, 0.95]  # 分位数范围 (适用于quantile策略)
  
  # 时序长度统一策略
  padding:
    target_length: 1500
    method: "reflect"         # ["reflect", "edge", "wrap", "constant", "resample"]
    truncate_mode: "center"   # ["start", "end", "center", "random"]
    
  # 数据质量控制
  quality_control:
    min_length: 300          # 最短有效长度
    max_length: 3000         # 最长有效长度
    nan_threshold: 0.05      # NaN值比例阈值
    outlier_detection:
      enabled: true
      method: "iqr"          # ["iqr", "zscore", "isolation_forest"]
      threshold: 3.0

# 噪声和增强配置 - 渐进式
augmentation:
  # 基础高斯噪声
  gaussian_noise:
    enabled: true
    std_range: [0.01, 0.05]  # 标准差范围
    probability: 0.3         # 应用概率
    
  # 预留扩展噪声类型
  powerline_noise:
    enabled: false           # 工频干扰 (50/60Hz)
    frequency: [50, 60]
    amplitude_range: [0.01, 0.03]
    probability: 0.2
    
  baseline_drift:
    enabled: false           # 基线漂移
    drift_rate_range: [0.001, 0.005]
    probability: 0.1
    
  # 时序变换
  temporal_augmentation:
    time_shift:
      enabled: false
      shift_range: [-50, 50]  # 时间点偏移
      probability: 0.2
    
    time_stretch:
      enabled: false
      stretch_range: [0.9, 1.1]  # 时间拉伸比例
      probability: 0.1

# 数据分割配置
data_split:
  # 对于MAE预训练，使用全部数据
  train_ratio: 1.0           # 第一阶段使用全部数据
  val_ratio: 0.0
  test_ratio: 0.0
  random_seed: 42
  
# 数据加载配置  
dataloader:
  batch_size: 32
  num_workers: 4
  pin_memory: true
  drop_last: true            # MAE训练建议丢弃不完整batch