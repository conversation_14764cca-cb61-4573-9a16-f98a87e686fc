# MCG第一阶段 MAE预训练配置
# 混合时空架构 + 渐进式掩码策略

# 实验基本信息
experiment:
  name: "mcg_mae_stage1"
  description: "MCG基础编码器预训练 - 混合时空MAE架构"
  stage: 1
  version: "1.0.0"

# 模型架构配置
model:
  # 基础编码器配置
  foundation_encoder:
    # 空间编码器 (36通道 -> 6x6网格)
    spatial_encoder:
      input_channels: 36
      spatial_dim: [6, 6]
      conv_channels: [64, 128, 256]
      kernel_sizes: [3, 3, 3]
      pooling: "adaptive"
      dropout: 0.1
      
    # 时间编码器 (Transformer)
    temporal_encoder:
      d_model: 256
      nhead: 8
      num_layers: 4
      dim_feedforward: 1024
      dropout: 0.1
      positional_encoding: true
      max_seq_len: 1500
      
    # 时空融合模块
    fusion_module:
      fusion_type: "cross_attention"  # ["concat", "add", "cross_attention"]
      hidden_dim: 512
      num_heads: 8
      dropout: 0.1
      
  # MAE特定配置
  mae_config:
    # 解码器配置
    decoder:
      d_model: 256
      nhead: 8  
      num_layers: 2
      dim_feedforward: 512
      
    # 掩码配置
    masking:
      # 混合掩码策略
      strategies:
        - type: "temporal_block"
          ratio: 0.2
          block_size_range: [20, 100]  # 时间块大小范围
          weight: 0.4
          
        - type: "spatial_block"  
          ratio: 0.15
          block_size: [2, 2]      # 空间块大小 (在6x6网格中)
          weight: 0.3
          
        - type: "random_point"
          ratio: 0.1
          weight: 0.3
          
      # 掩码调度 (渐进式增加难度)
      scheduling:
        enabled: true
        initial_ratio: 0.15      # 初始掩码比例
        final_ratio: 0.25        # 最终掩码比例
        warmup_epochs: 20
        
    # 重建目标配置
    reconstruction:
      target: "denoised"         # ["original", "denoised"]
      loss_type: "mse"          # ["mse", "l1", "huber"]
      pixel_wise: true
      normalize_target: true

# 训练配置
training:
  # 优化器配置
  optimizer:
    type: "AdamW"
    lr: 1e-4
    weight_decay: 0.05
    betas: [0.9, 0.95]
    
  # 学习率调度
  lr_scheduler:
    type: "cosine_with_warmup"
    warmup_epochs: 10
    min_lr_ratio: 0.1
    
  # 训练参数
  epochs: 100
  gradient_clip_norm: 1.0
  gradient_accumulation_steps: 1
  
  # 早停配置
  early_stopping:
    patience: 15
    min_delta: 1e-4
    monitor: "val_reconstruction_loss"
    mode: "min"
    
  # 检查点保存
  checkpointing:
    save_interval: 5          # 每5个epoch保存一次
    save_best_only: false     # 保存所有检查点用于阶段二
    save_last_k: 3           # 保留最近3个检查点
    
# 评估配置  
evaluation:
  # 验证频率
  val_interval: 2             # 每2个epoch验证一次
  
  # 评估指标
  metrics:
    reconstruction:
      - "mse"
      - "mae" 
      - "psnr"
      
    representation:
      - "feature_diversity"    # 特征多样性
      - "spatial_correlation"  # 空间相关性保持
      - "temporal_consistency" # 时间一致性
      
  # 可视化配置
  visualization:
    enabled: true
    save_interval: 10         # 每10个epoch保存可视化结果
    samples_per_vis: 4        # 每次可视化的样本数
    vis_types:
      - "reconstruction_comparison"
      - "masking_visualization" 
      - "attention_maps"
      - "feature_tsne"

# 系统配置
system:
  # 设备配置
  device: "cuda"
  mixed_precision: true       # 使用混合精度训练
  
  # 随机种子
  random_seed: 42
  deterministic: true
  
  # 日志配置
  logging:
    log_level: "INFO"
    log_dir: "logs/stage1_mae"
    tensorboard: true
    wandb:
      enabled: false          # 可选择启用
      project: "mcg_mae_stage1"
      
  # 资源监控
  monitoring:
    track_gpu_memory: true
    track_cpu_usage: true
    profile_training: false   # 性能分析 (调试时启用)