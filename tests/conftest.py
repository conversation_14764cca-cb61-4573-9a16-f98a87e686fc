"""
pytest配置文件
提供测试fixtures和共享配置
"""

import pytest
import numpy as np
import torch
import tempfile
from pathlib import Path
from typing import Dict, Any, List

from src.utils.config import ConfigManager
from src.data.preprocessing import MCGPreprocessor
from src.models.mae.mae_model import MCGMAE


@pytest.fixture
def temp_data_dir():
    """创建临时数据目录"""
    with tempfile.TemporaryDirectory() as tmpdir:
        temp_path = Path(tmpdir)
        yield temp_path


@pytest.fixture
def sample_mcg_data():
    """生成示例MCG数据"""
    np.random.seed(42)
    # 创建36通道，1000个时间点的示例数据
    data = np.random.randn(1000, 36) * 0.1
    
    # 添加一些生理信号特征
    time = np.linspace(0, 1, 1000)
    for channel in range(36):
        # 添加心跳信号（约1Hz）
        heartbeat = 0.5 * np.sin(2 * np.pi * 1.0 * time + np.random.rand() * 2 * np.pi)
        # 添加呼吸信号（约0.25Hz）
        breathing = 0.2 * np.sin(2 * np.pi * 0.25 * time + np.random.rand() * 2 * np.pi)
        data[:, channel] += heartbeat + breathing
    
    return data


@pytest.fixture
def sample_mcg_files(temp_data_dir, sample_mcg_data):
    """创建示例MCG文件"""
    file_paths = []
    for i in range(5):
        file_path = temp_data_dir / f"sample_{i:03d}.txt"
        # 为每个文件添加一些变化
        noise = np.random.randn(*sample_mcg_data.shape) * 0.01
        data = sample_mcg_data + noise
        np.savetxt(file_path, data)
        file_paths.append(file_path)
    
    return file_paths


@pytest.fixture
def test_config():
    """测试用配置"""
    config = {
        'data': {
            'txt_data_dir': '/tmp/test_data',
            'expected_channels': 36,
            'min_signal_length': 500,
            'max_signal_length': 2000,
            'train_ratio': 0.8,
            'val_ratio': 0.1,
            'test_ratio': 0.1
        },
        'preprocessing': {
            'normalization_strategy': 'sample_level',
            'padding_strategy': 'zero',
            'target_length': 1000,
            'noise_level': 0.01
        },
        'model': {
            'spatial_encoder': {
                'hidden_dims': [64, 128],
                'kernel_sizes': [3, 3],
                'stride': 1,
                'padding': 1,
                'dropout': 0.1
            },
            'temporal_encoder': {
                'd_model': 256,
                'nhead': 8,
                'num_layers': 6,
                'dim_feedforward': 1024,
                'dropout': 0.1,
                'max_seq_len': 1000
            },
            'fusion': {
                'strategy': 'cross_attention',
                'hidden_dim': 256,
                'num_heads': 8,
                'dropout': 0.1
            }
        },
        'mae': {
            'masking_strategy': 'temporal_block',
            'mask_ratio': 0.75,
            'decoder': {
                'd_model': 256,
                'nhead': 8,
                'num_layers': 4,
                'dim_feedforward': 512
            }
        },
        'training': {
            'batch_size': 32,
            'learning_rate': 1e-4,
            'num_epochs': 100,
            'warmup_steps': 1000,
            'weight_decay': 0.05,
            'mixed_precision': True
        }
    }
    
    return config


@pytest.fixture
def config_manager(test_config):
    """配置管理器fixture"""
    manager = ConfigManager()
    manager.config = test_config
    return manager


@pytest.fixture
def mcg_preprocessor(test_config):
    """MCG预处理器fixture"""
    return MCGPreprocessor(config=test_config['preprocessing'])


@pytest.fixture
def mae_model(test_config):
    """MAE模型fixture"""
    torch.manual_seed(42)
    model = MCGMAE(
        spatial_config=test_config['model']['spatial_encoder'],
        temporal_config=test_config['model']['temporal_encoder'],
        fusion_config=test_config['model']['fusion'],
        mae_config=test_config['mae']
    )
    return model


@pytest.fixture
def sample_batch():
    """示例批次数据"""
    torch.manual_seed(42)
    batch_size = 4
    seq_len = 1000
    channels = 36
    
    # 生成示例批次
    batch = torch.randn(batch_size, seq_len, channels)
    return batch


@pytest.fixture(scope="session")
def device():
    """获取可用设备"""
    return torch.device('cuda' if torch.cuda.is_available() else 'cpu')


@pytest.fixture
def mock_data_files(temp_data_dir):
    """创建各种类型的测试数据文件"""
    files = {}
    
    # 正常文件
    normal_data = np.random.randn(1000, 36)
    normal_file = temp_data_dir / "normal.txt"
    np.savetxt(normal_file, normal_data)
    files['normal'] = normal_file
    
    # 空文件
    empty_file = temp_data_dir / "empty.txt"
    empty_file.touch()
    files['empty'] = empty_file
    
    # 损坏文件
    corrupted_file = temp_data_dir / "corrupted.txt"
    with open(corrupted_file, 'w') as f:
        f.write("invalid data format")
    files['corrupted'] = corrupted_file
    
    # 维度不匹配文件
    wrong_dims_data = np.random.randn(1000, 20)  # 20通道而不是36
    wrong_dims_file = temp_data_dir / "wrong_dims.txt"
    np.savetxt(wrong_dims_file, wrong_dims_data)
    files['wrong_dims'] = wrong_dims_file
    
    # NaN值文件
    nan_data = np.random.randn(1000, 36)
    nan_data[100:110, 5] = np.nan
    nan_file = temp_data_dir / "nan_data.txt"
    np.savetxt(nan_file, nan_data)
    files['nan_data'] = nan_file
    
    # 零方差文件
    zero_var_data = np.random.randn(1000, 36)
    zero_var_data[:, 10] = 1.0  # 常数通道
    zero_var_file = temp_data_dir / "zero_var.txt"
    np.savetxt(zero_var_file, zero_var_data)
    files['zero_var'] = zero_var_file
    
    return files


# 测试标记
pytest_slow = pytest.mark.slow
pytest_gpu = pytest.mark.skipif(not torch.cuda.is_available(), reason="CUDA not available")
pytest_integration = pytest.mark.integration