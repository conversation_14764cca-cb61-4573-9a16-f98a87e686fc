"""
数据预处理模块的单元测试
"""

import pytest
import numpy as np
import torch
from pathlib import Path

from src.data.preprocessing import MCGPreprocessor, MCGAugmentor
from src.data.dataset import BaseMCGDataset, MAEPretrainDataset


class TestMCGPreprocessor:
    """MCG预处理器测试类"""
    
    def test_init_default_config(self):
        """测试默认配置初始化"""
        preprocessor = MCGPreprocessor()
        
        assert preprocessor.normalization_strategy == 'sample_level'
        assert preprocessor.padding_strategy == 'zero'
        assert preprocessor.target_length == 1000
        assert preprocessor.noise_level == 0.0
    
    def test_init_custom_config(self, test_config):
        """测试自定义配置初始化"""
        config = test_config['preprocessing']
        preprocessor = MCGPreprocessor(config)
        
        assert preprocessor.normalization_strategy == config['normalization_strategy']
        assert preprocessor.padding_strategy == config['padding_strategy']
        assert preprocessor.target_length == config['target_length']
        assert preprocessor.noise_level == config['noise_level']
    
    def test_sample_level_normalization(self, mcg_preprocessor, sample_mcg_data):
        """测试样本级归一化"""
        mcg_preprocessor.normalization_strategy = 'sample_level'
        normalized = mcg_preprocessor.normalize(sample_mcg_data.copy())
        
        # 检查每个样本的均值约为0，标准差约为1
        assert np.allclose(np.mean(normalized), 0, atol=1e-10)
        assert np.allclose(np.std(normalized), 1, atol=1e-10)
    
    def test_channel_wise_normalization(self, mcg_preprocessor, sample_mcg_data):
        """测试通道级归一化"""
        mcg_preprocessor.normalization_strategy = 'channel_wise'
        normalized = mcg_preprocessor.normalize(sample_mcg_data.copy())
        
        # 检查每个通道的均值约为0，标准差约为1
        channel_means = np.mean(normalized, axis=0)
        channel_stds = np.std(normalized, axis=0)
        
        assert np.allclose(channel_means, 0, atol=1e-10)
        assert np.allclose(channel_stds, 1, atol=1e-10)
    
    def test_global_normalization(self, mcg_preprocessor, sample_mcg_data):
        """测试全局归一化"""
        mcg_preprocessor.normalization_strategy = 'global'
        
        # 模拟预计算的全局统计
        global_mean = np.mean(sample_mcg_data)
        global_std = np.std(sample_mcg_data)
        mcg_preprocessor.global_stats = {'mean': global_mean, 'std': global_std}
        
        normalized = mcg_preprocessor.normalize(sample_mcg_data.copy())
        expected = (sample_mcg_data - global_mean) / global_std
        
        assert np.allclose(normalized, expected)
    
    def test_robust_normalization(self, mcg_preprocessor, sample_mcg_data):
        """测试鲁棒归一化"""
        mcg_preprocessor.normalization_strategy = 'robust'
        normalized = mcg_preprocessor.normalize(sample_mcg_data.copy())
        
        # 检查归一化后的中位数约为0
        assert np.allclose(np.median(normalized), 0, atol=0.1)
    
    def test_zero_padding(self, mcg_preprocessor, sample_mcg_data):
        """测试零填充"""
        mcg_preprocessor.padding_strategy = 'zero'
        mcg_preprocessor.target_length = 1500
        
        padded = mcg_preprocessor.apply_padding(sample_mcg_data)
        
        assert padded.shape[0] == 1500
        assert padded.shape[1] == sample_mcg_data.shape[1]
        # 检查填充部分为零
        assert np.all(padded[1000:] == 0)
    
    def test_reflect_padding(self, mcg_preprocessor, sample_mcg_data):
        """测试反射填充"""
        mcg_preprocessor.padding_strategy = 'reflect'
        mcg_preprocessor.target_length = 1200
        
        padded = mcg_preprocessor.apply_padding(sample_mcg_data)
        
        assert padded.shape[0] == 1200
        assert padded.shape[1] == sample_mcg_data.shape[1]
        # 检查反射填充
        expected_reflect = sample_mcg_data[-200:][::-1]  # 最后200个点的反射
        assert np.allclose(padded[1000:], expected_reflect, atol=1e-10)
    
    def test_truncation(self, mcg_preprocessor, sample_mcg_data):
        """测试截断"""
        mcg_preprocessor.target_length = 800
        
        processed = mcg_preprocessor.apply_padding(sample_mcg_data)
        
        assert processed.shape[0] == 800
        assert np.allclose(processed, sample_mcg_data[:800])
    
    def test_add_noise(self, mcg_preprocessor, sample_mcg_data):
        """测试添加噪声"""
        mcg_preprocessor.noise_level = 0.1
        
        noisy = mcg_preprocessor.add_noise(sample_mcg_data.copy())
        
        # 噪声应该改变信号
        assert not np.allclose(noisy, sample_mcg_data)
        # 但差异应该不太大
        assert np.std(noisy - sample_mcg_data) < 0.2
    
    def test_process_signal_pipeline(self, mcg_preprocessor, sample_mcg_data):
        """测试完整的信号处理流水线"""
        mcg_preprocessor.normalization_strategy = 'sample_level'
        mcg_preprocessor.target_length = 1200
        mcg_preprocessor.noise_level = 0.05
        
        processed = mcg_preprocessor.process_signal(sample_mcg_data)
        
        # 检查输出维度
        assert processed.shape == (1200, 36)
        # 检查归一化效果
        assert abs(np.mean(processed)) < 0.1
        assert abs(np.std(processed) - 1.0) < 0.1
    
    def test_invalid_normalization_strategy(self):
        """测试无效的归一化策略"""
        with pytest.raises(ValueError):
            MCGPreprocessor({'normalization_strategy': 'invalid'})
    
    def test_invalid_padding_strategy(self):
        """测试无效的填充策略"""
        with pytest.raises(ValueError):
            MCGPreprocessor({'padding_strategy': 'invalid'})
    
    def test_empty_signal(self, mcg_preprocessor):
        """测试空信号处理"""
        empty_signal = np.array([]).reshape(0, 36)
        
        with pytest.raises(ValueError):
            mcg_preprocessor.process_signal(empty_signal)
    
    def test_single_channel_signal(self, mcg_preprocessor):
        """测试单通道信号"""
        single_channel = np.random.randn(1000, 1)
        processed = mcg_preprocessor.process_signal(single_channel)
        
        assert processed.shape[1] == 1
        assert processed.shape[0] == mcg_preprocessor.target_length


class TestMCGAugmentor:
    """MCG数据增强器测试类"""
    
    def test_time_shift(self, sample_mcg_data):
        """测试时间偏移增强"""
        augmentor = MCGAugmentor({'time_shift': {'max_shift': 100}})
        
        augmented = augmentor.apply_augmentation(sample_mcg_data, 'time_shift')
        
        # 形状应该保持不变
        assert augmented.shape == sample_mcg_data.shape
        # 内容应该有所变化
        assert not np.allclose(augmented, sample_mcg_data)
    
    def test_amplitude_scaling(self, sample_mcg_data):
        """测试幅度缩放增强"""
        augmentor = MCGAugmentor({'amplitude_scaling': {'scale_range': [0.8, 1.2]}})
        
        augmented = augmentor.apply_augmentation(sample_mcg_data, 'amplitude_scaling')
        
        # 形状应该保持不变
        assert augmented.shape == sample_mcg_data.shape
        # 检查缩放范围
        scale_factor = np.mean(augmented) / np.mean(sample_mcg_data)
        assert 0.7 < abs(scale_factor) < 1.3
    
    def test_gaussian_noise(self, sample_mcg_data):
        """测试高斯噪声增强"""
        augmentor = MCGAugmentor({'gaussian_noise': {'noise_std': 0.1}})
        
        augmented = augmentor.apply_augmentation(sample_mcg_data, 'gaussian_noise')
        
        # 形状应该保持不变
        assert augmented.shape == sample_mcg_data.shape
        # 噪声应该改变信号
        assert not np.allclose(augmented, sample_mcg_data)
        # 但变化不应过大
        assert np.std(augmented - sample_mcg_data) < 0.2
    
    def test_frequency_masking(self, sample_mcg_data):
        """测试频域掩码增强"""
        augmentor = MCGAugmentor({'frequency_masking': {'mask_ratio': 0.1}})
        
        augmented = augmentor.apply_augmentation(sample_mcg_data, 'frequency_masking')
        
        # 形状应该保持不变
        assert augmented.shape == sample_mcg_data.shape
        # 应该有所变化
        assert not np.allclose(augmented, sample_mcg_data)
    
    def test_random_augmentation(self, sample_mcg_data):
        """测试随机增强选择"""
        augmentor = MCGAugmentor({
            'time_shift': {'max_shift': 50},
            'gaussian_noise': {'noise_std': 0.05}
        })
        
        # 多次运行以测试随机性
        results = []
        for _ in range(10):
            augmented = augmentor.random_augment(sample_mcg_data)
            results.append(augmented)
        
        # 结果应该有变化（不是所有都相同）
        unique_results = len(set([r.tobytes() for r in results]))
        assert unique_results > 1
    
    def test_invalid_augmentation_type(self, sample_mcg_data):
        """测试无效的增强类型"""
        augmentor = MCGAugmentor()
        
        with pytest.raises(ValueError):
            augmentor.apply_augmentation(sample_mcg_data, 'invalid_type')


class TestBaseMCGDataset:
    """基础MCG数据集测试类"""
    
    def test_init(self, sample_mcg_files, test_config):
        """测试数据集初始化"""
        dataset = BaseMCGDataset(
            data_dir=sample_mcg_files[0].parent,
            config=test_config['data']
        )
        
        assert len(dataset.file_paths) == 5
        assert dataset.expected_channels == 36
    
    def test_len(self, sample_mcg_files, test_config):
        """测试数据集长度"""
        dataset = BaseMCGDataset(
            data_dir=sample_mcg_files[0].parent,
            config=test_config['data']
        )
        
        assert len(dataset) == 5
    
    def test_getitem(self, sample_mcg_files, test_config):
        """测试获取数据项"""
        dataset = BaseMCGDataset(
            data_dir=sample_mcg_files[0].parent,
            config=test_config['data'],
            preprocessing_config=test_config['preprocessing']
        )
        
        signal = dataset[0]
        
        assert isinstance(signal, torch.Tensor)
        assert signal.shape[0] == test_config['preprocessing']['target_length']
        assert signal.shape[1] == test_config['data']['expected_channels']
    
    def test_caching(self, sample_mcg_files, test_config):
        """测试缓存功能"""
        dataset = BaseMCGDataset(
            data_dir=sample_mcg_files[0].parent,
            config=test_config['data'],
            preprocessing_config=test_config['preprocessing'],
            cache_size=3
        )
        
        # 第一次访问
        signal1 = dataset[0]
        # 第二次访问应该从缓存获取
        signal2 = dataset[0]
        
        assert torch.equal(signal1, signal2)
        assert len(dataset.cache) == 1
    
    def test_cache_eviction(self, sample_mcg_files, test_config):
        """测试缓存淘汰"""
        dataset = BaseMCGDataset(
            data_dir=sample_mcg_files[0].parent,
            config=test_config['data'],
            preprocessing_config=test_config['preprocessing'],
            cache_size=2
        )
        
        # 访问多个数据项
        dataset[0]  # 缓存项0
        dataset[1]  # 缓存项1
        dataset[2]  # 应该淘汰项0，缓存项2
        
        assert len(dataset.cache) == 2
        assert 0 not in dataset.cache


class TestMAEPretrainDataset:
    """MAE预训练数据集测试类"""
    
    def test_init(self, sample_mcg_files, test_config):
        """测试MAE数据集初始化"""
        dataset = MAEPretrainDataset(
            data_dir=sample_mcg_files[0].parent,
            config=test_config['data'],
            preprocessing_config=test_config['preprocessing']
        )
        
        assert dataset.augmentor is not None
    
    def test_getitem_with_augmentation(self, sample_mcg_files, test_config):
        """测试带增强的数据获取"""
        # 启用增强
        test_config['preprocessing']['augmentation'] = {
            'gaussian_noise': {'noise_std': 0.05}
        }
        
        dataset = MAEPretrainDataset(
            data_dir=sample_mcg_files[0].parent,
            config=test_config['data'],
            preprocessing_config=test_config['preprocessing']
        )
        
        signal1 = dataset[0]
        signal2 = dataset[0]  # 同一个索引，但应该有不同的增强
        
        # 形状应该相同
        assert signal1.shape == signal2.shape
        # 由于随机增强，内容应该不同
        assert not torch.equal(signal1, signal2)
    
    def test_getitem_without_augmentation(self, sample_mcg_files, test_config):
        """测试不带增强的数据获取"""
        dataset = MAEPretrainDataset(
            data_dir=sample_mcg_files[0].parent,
            config=test_config['data'],
            preprocessing_config=test_config['preprocessing']
        )
        
        signal = dataset[0]
        
        assert isinstance(signal, torch.Tensor)
        assert signal.shape[0] == test_config['preprocessing']['target_length']
        assert signal.shape[1] == test_config['data']['expected_channels']