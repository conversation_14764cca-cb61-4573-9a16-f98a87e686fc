#!/usr/bin/env python3
"""
MCG Phase 1 MAE预训练主脚本
==========================

整合配置加载、模型创建、训练执行的完整训练流程
"""

import os
import sys
import yaml
import torch
import logging
import argparse
from pathlib import Path
from datetime import datetime

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def setup_logging(log_dir: Path, log_level: str = "INFO"):
    """设置日志系统"""
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"mae_training_{timestamp}.log"
    
    # 配置日志格式
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    return logger

def load_configs(preprocessing_config_path: str, mae_config_path: str):
    """加载并合并配置文件"""
    
    # 加载预处理配置
    with open(preprocessing_config_path, 'r', encoding='utf-8') as f:
        preprocessing_config = yaml.safe_load(f)
    
    # 加载MAE配置
    with open(mae_config_path, 'r', encoding='utf-8') as f:
        mae_config = yaml.safe_load(f)
    
    # 合并配置
    merged_config = {
        # 数据相关配置来自preprocessing.yaml
        'data_config': preprocessing_config['data_config'],
        'data_split': preprocessing_config['data_split'],  # 添加缺失的data_split
        'preprocessing': preprocessing_config['preprocessing'],
        'augmentation': preprocessing_config['augmentation'],
        'dataloader': preprocessing_config['dataloader'],
        
        # 模型配置来自stage1_mae.yaml，需要转换格式
        'model': {
            'encoder': {
                'hidden_dim': mae_config['model']['foundation_encoder']['fusion_module']['hidden_dim'],
                'spatial_encoder': {
                    'type': 'mcg_spatial',
                    'input_channels': mae_config['model']['foundation_encoder']['spatial_encoder']['input_channels'],
                    'conv_channels': mae_config['model']['foundation_encoder']['spatial_encoder']['conv_channels'],
                    'dropout': mae_config['model']['foundation_encoder']['spatial_encoder']['dropout'],
                    'pooling': mae_config['model']['foundation_encoder']['spatial_encoder']['pooling']
                },
                'temporal_encoder': {
                    'type': 'mcg_temporal',
                    'd_model': mae_config['model']['foundation_encoder']['temporal_encoder']['d_model'],
                    'nhead': mae_config['model']['foundation_encoder']['temporal_encoder']['nhead'],
                    'num_layers': mae_config['model']['foundation_encoder']['temporal_encoder']['num_layers'],
                    'dropout': mae_config['model']['foundation_encoder']['temporal_encoder']['dropout'],
                    'global_pooling': 'attention'
                },
                'fusion_module': {
                    'fusion_type': mae_config['model']['foundation_encoder']['fusion_module']['fusion_type'],
                    'spatial_dim': 256,  # 空间编码器输出维度
                    'temporal_dim': mae_config['model']['foundation_encoder']['temporal_encoder']['d_model'],
                    'hidden_dim': mae_config['model']['foundation_encoder']['fusion_module']['hidden_dim'],
                    'dropout': mae_config['model']['foundation_encoder']['fusion_module']['dropout']
                }
            },
            'decoder': {
                'encoder_dim': mae_config['model']['foundation_encoder']['fusion_module']['hidden_dim'],
                'decoder_dim': mae_config['model']['mae_config']['decoder']['d_model'],
                'num_heads': mae_config['model']['mae_config']['decoder']['nhead'],
                'num_layers': mae_config['model']['mae_config']['decoder']['num_layers'],
                'output_channels': mae_config['model']['foundation_encoder']['spatial_encoder']['input_channels'],
                'output_seq_len': preprocessing_config['data_config']['target_length']
            },
            'masking': {
                'type': 'temporal_block',  # 简化为单一策略
                'mask_ratio': 0.2,
                'block_size_range': [20, 100]
            },
            'loss_type': mae_config['model']['mae_config']['reconstruction']['loss_type'],
            'normalize_target': mae_config['model']['mae_config']['reconstruction']['normalize_target'],
            'mask_loss_only': True
        },
        
        # 训练配置
        'epochs': mae_config['training']['epochs'],
        'optimizer': {
            'type': mae_config['training']['optimizer']['type'],
            'lr': float(mae_config['training']['optimizer']['lr']),  # 确保lr是浮点数
            'weight_decay': mae_config['training']['optimizer']['weight_decay'],
            'betas': mae_config['training']['optimizer']['betas']
        },
        'scheduler': {
            'enabled': True,
            'type': mae_config['training']['lr_scheduler']['type'],
            'warmup_ratio': mae_config['training']['lr_scheduler']['warmup_epochs'] / mae_config['training']['epochs']
        },
        'mixed_precision': mae_config['system']['mixed_precision'],
        'gradient_clip_norm': mae_config['training']['gradient_clip_norm'],
        'gradient_accumulation_steps': mae_config['training']['gradient_accumulation_steps'],
        'eval_interval': mae_config['evaluation']['val_interval'],
        'save_interval': mae_config['training']['checkpointing']['save_interval'],
        'log_interval': 50,
        'early_stopping': mae_config['training']['early_stopping'],
        'validation': {'enabled': True},
        
        # 实验配置
        'experiment_name': mae_config['experiment']['name'],
        'experiment_dir': f"experiments/{mae_config['experiment']['name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        
        # 系统配置
        'random_seed': mae_config['system']['random_seed'],
        'device': mae_config['system']['device']
    }
    
    return merged_config

def setup_experiment_dir(experiment_dir: str):
    """创建实验目录结构"""
    exp_path = Path(experiment_dir)
    exp_path.mkdir(parents=True, exist_ok=True)
    
    # 创建子目录
    (exp_path / 'checkpoints').mkdir(exist_ok=True)
    (exp_path / 'logs').mkdir(exist_ok=True)
    (exp_path / 'visualizations').mkdir(exist_ok=True)
    
    return exp_path

def check_dependencies():
    """检查关键依赖"""
    try:
        import transformers
    except ImportError:
        print("警告: transformers未安装，正在安装...")
        os.system("pip install transformers")
    
    try:
        import tensorboard
    except ImportError:
        print("警告: tensorboard未安装，正在安装...")
        os.system("pip install tensorboard")

def main():
    parser = argparse.ArgumentParser(description='MCG Phase 1 MAE预训练')
    parser.add_argument('--preprocessing-config', default='configs/preprocessing.yaml',
                       help='预处理配置文件路径')
    parser.add_argument('--mae-config', default='configs/stage1_mae.yaml',
                       help='MAE配置文件路径')
    parser.add_argument('--resume', type=str, default=None,
                       help='从检查点恢复训练')
    parser.add_argument('--debug', action='store_true',
                       help='调试模式（使用少量数据）')
    
    args = parser.parse_args()
    
    # 检查依赖
    check_dependencies()
    
    # 加载配置
    print("加载配置文件...")
    config = load_configs(args.preprocessing_config, args.mae_config)
    
    # 调试模式调整
    if args.debug:
        print("🐛 调试模式启用")
        config['epochs'] = 2
        config['dataloader']['batch_size'] = 1  # 减小批次大小
        config['dataloader']['num_workers'] = 0
        config['eval_interval'] = 1
        config['save_interval'] = 1
        config['experiment_name'] += '_debug'

        # 减小模型大小以适应GPU内存，确保维度一致
        config['model']['encoder']['hidden_dim'] = 64
        config['model']['encoder']['spatial_encoder']['conv_channels'] = [16, 32, 64]  # 最后一层输出64维
        config['model']['encoder']['temporal_encoder']['d_model'] = 64
        config['model']['encoder']['temporal_encoder']['num_layers'] = 2
        config['model']['encoder']['fusion_module']['hidden_dim'] = 64
        config['model']['encoder']['fusion_module']['spatial_dim'] = 64  # 匹配空间编码器输出
        config['model']['encoder']['fusion_module']['temporal_dim'] = 64  # 匹配时间编码器输出
        config['model']['decoder']['encoder_dim'] = 64
        config['model']['decoder']['decoder_dim'] = 32
        config['model']['decoder']['num_layers'] = 1
    
    # 设置实验目录
    exp_dir = setup_experiment_dir(config['experiment_dir'])
    config['experiment_dir'] = str(exp_dir)
    
    # 设置日志
    logger = setup_logging(exp_dir / 'logs')
    
    # 保存配置
    with open(exp_dir / 'config.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    logger.info(f"实验配置已保存到: {exp_dir / 'config.yaml'}")
    logger.info(f"实验目录: {exp_dir}")
    
    # 设置随机种子
    torch.manual_seed(config['random_seed'])
    
    # 创建训练器并开始训练
    try:
        from training.mae_trainer import create_mae_trainer
        
        data_root = config['data_config']['txt_data_dir']
        logger.info(f"数据路径: {data_root}")
        
        # 创建训练器
        trainer = create_mae_trainer(config, data_root)
        
        # 如果需要从检查点恢复
        if args.resume:
            logger.info(f"从检查点恢复训练: {args.resume}")
            trainer.load_checkpoint(args.resume, resume_training=True)
        
        # 开始训练
        logger.info("开始MAE预训练...")
        trainer.train()
        
        logger.info("训练完成！")
        logger.info(f"最佳模型保存在: {exp_dir / 'checkpoints' / 'best_model.pth'}")
        
    except Exception as e:
        logger.error(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
